steps:
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    entrypoint: 'npm'
    args: ['install']

  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    entrypoint: 'npm'
    args: ['run', 'auth']

  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    entrypoint: 'npm'
    args:
      [
        'run',
        'prerelease:version',
        '--workspaces',
        '--',
        '--suffix="$SHORT_SHA.$_REVISION"',
      ]

  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    entrypoint: 'npm'
    args: ['run', 'prerelease:deps', '--workspaces']

  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    entrypoint: 'npm'
    args:
      ['publish', '--tag=head', '--dry-run', '--workspace=@google/gemini-cli']

options:
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET
