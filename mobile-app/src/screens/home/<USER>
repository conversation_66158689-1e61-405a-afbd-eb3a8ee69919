import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  FlatList,
  RefreshControl,
} from "react-native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RouteProp } from "@react-navigation/native";
import { HomeStackParamList } from "../../navigation/types";
import MallSearchBar from "../../components/MallSearchBar";

type HomeScreenNavigationProp = NativeStackNavigationProp<
  HomeStackParamList,
  "Home"
>;

type HomeScreenRouteProp = RouteProp<HomeStackParamList, "Home">;

interface Props {
  navigation: HomeScreenNavigationProp;
  route: HomeScreenRouteProp;
}

const { width } = Dimensions.get("window");

const HomeScreen: React.FC<Props> = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const bannerRef = useRef<FlatList>(null);

  // Banner輪播圖數據 - 使用模擬數據，實際開發中會從API獲取
  const banners = [
    {
      id: "1",
      image: {
        uri: "https://images.unsplash.com/photo-1543362906-acfc16c67564?w=400&h=280&fit=crop",
      },
      title: "WORLD WOMEN'S SNOOKER CHAMPIONSHIP 2024",
    },
    {
      id: "2",
      image: {
        uri: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=280&fit=crop",
      },
      title: "專業台球教學培訓",
    },
    {
      id: "3",
      image: {
        uri: "https://images.unsplash.com/photo-1574680178050-55c6a6a96e0a?w=400&h=280&fit=crop",
      },
      title: "一對一專業指導",
    },
    {
      id: "4",
      image: {
        uri: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=280&fit=crop",
      },
      title: "團體課程培訓",
    },
  ];

  // 自動輪播
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentBannerIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % banners.length;
        bannerRef.current?.scrollToIndex({ index: nextIndex, animated: true });
        return nextIndex;
      });
    }, 4000); // 4秒切換一次

    return () => clearInterval(timer);
  }, [banners.length]);

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const handleSearch = () => {
    navigation.navigate("Search");
  };

  const renderBanner = ({ item }: { item: any }) => (
    <View style={styles.bannerItem}>
      <Image
        source={item.image}
        style={styles.bannerImage}
        resizeMode="cover"
      />

      <View style={styles.bannerOverlay}>
        <Text style={styles.bannerTitle}>{item.title}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Banner輪播圖 */}
        <View style={styles.bannerContainer}>
          <FlatList
            ref={bannerRef}
            data={banners}
            renderItem={renderBanner}
            keyExtractor={(item) => item.id}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={(event) => {
              const index = Math.round(
                event.nativeEvent.contentOffset.x / width,
              );
              setCurrentBannerIndex(index);
            }}
          />

          {/* 輪播圖指示點 */}
          <View style={styles.bannerDots}>
            {banners.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.dot,
                  index === currentBannerIndex && styles.activeDot,
                ]}
              />
            ))}
          </View>
        </View>

        {/* 搜索組件 */}
        <MallSearchBar
          placeholder="搜索商品、教練"
          onPress={handleSearch}
          editable={false}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  content: {
    flex: 1,
  },
  bannerContainer: {
    height: 280,
    position: "relative",
  },
  bannerItem: {
    width: width,
    height: 280,
    position: "relative",
  },
  bannerImage: {
    width: "100%",
    height: "100%",
  },
  bannerOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: 20,
  },
  bannerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
    textShadowColor: "rgba(0, 0, 0, 0.7)",
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  bannerDots: {
    position: "absolute",
    bottom: 30,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "rgba(255, 255, 255, 0.5)",
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: "#fff",
    width: 24,
    borderRadius: 4,
  },
});

export default HomeScreen;
