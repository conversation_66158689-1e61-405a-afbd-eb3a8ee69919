import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { useRoute, useNavigation } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { fetchPackages } from "../../store/slices/packageSlice";
import { fetchCoaches } from "../../store/slices/coachSlice";

// 导入组件
import SearchBar from "../../components/SearchBar";
import ProductCard from "../../components/ProductCard";
import CoachCard from "../../components/CoachCard";

interface SearchScreenProps {
  route: {
    params: {
      type: "product" | "coach";
      query?: string;
    };
  };
}

const SearchScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const { type, query: initialQuery } = route.params as any;
  const [searchQuery, setSearchQuery] = useState(initialQuery || "");
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const { packages } = useSelector((state: RootState) => state.packages);
  const { coaches } = useSelector((state: RootState) => state.coach);

  useEffect(() => {
    if (initialQuery) {
      handleSearch(initialQuery);
    }
  }, [initialQuery]);

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setLoading(true);
    setSearchQuery(query);

    try {
      if (type === "product") {
        await dispatch(fetchPackages({ search: query }) as any);
        const filteredPackages = packages.filter(
          (pkg) =>
            pkg.name.toLowerCase().includes(query.toLowerCase()) ||
            pkg.description.toLowerCase().includes(query.toLowerCase()) ||
            pkg.category.toLowerCase().includes(query.toLowerCase()),
        );
        setResults(filteredPackages);
      } else {
        await dispatch(fetchCoaches({ search: query }) as any);
        const filteredCoaches = coaches.filter(
          (coach) =>
            coach.user?.nickname.toLowerCase().includes(query.toLowerCase()) ||
            coach.specialties?.some((specialty) =>
              specialty.toLowerCase().includes(query.toLowerCase()),
            ),
        );
        setResults(filteredCoaches);
      }
    } catch (error) {
      console.error("搜索失败:", error);
    } finally {
      setLoading(false);
    }
  };

  const renderProductItem = ({ item }: { item: any }) => (
    <ProductCard
      package={item}
      onPress={() =>
        navigation.navigate("PackageDetail" as never, { packageId: item.id })
      }
    />
  );

  const renderCoachItem = ({ item }: { item: any }) => (
    <CoachCard
      coach={item}
      onPress={() =>
        navigation.navigate("CoachDetail" as never, { coachId: item.id })
      }
    />
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        {searchQuery ? "没有找到相关结果" : "请输入搜索关键词"}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <SearchBar
          placeholder={type === "product" ? "搜索课程包..." : "搜索教练..."}
          onSearch={handleSearch}
          value={searchQuery}
        />
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#5a9178" />
          <Text style={styles.loadingText}>搜索中...</Text>
        </View>
      ) : (
        <FlatList
          data={results}
          renderItem={type === "product" ? renderProductItem : renderCoachItem}
          keyExtractor={(item) => item.id}
          numColumns={type === "product" ? 2 : 1}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={renderEmptyComponent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e1e1",
  },
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: "#666",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 16,
    color: "#999",
    textAlign: "center",
  },
});

export default SearchScreen;
