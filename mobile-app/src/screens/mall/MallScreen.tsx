import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  SafeAreaView,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import Icon from "react-native-vector-icons/MaterialIcons";
import { RootState } from "../../store";
import { fetchPackages } from "../../store/slices/packageSlice";

// 導入新建組件
import MallHeader from "../../components/MallHeader";
import MallSearchBar from "../../components/MallSearchBar";
import CategoryTabs from "../../components/CategoryTabs";
import MallProductCard from "../../components/MallProductCard";

const MallScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("brand");
  const [searchQuery, setSearchQuery] = useState("");

  const { packages, loading, pagination } = useSelector(
    (state: RootState) => state.packages,
  );

  // 分類數據 - 根據截圖
  const categories = [
    { id: "brand", name: "品牌" },
    { id: "category", name: "品類" },
    { id: "sales", name: "銷量" },
    { id: "price", name: "價格" },
  ];

  // 模擬商品數據 - 符合截圖風格
  const mockProducts = [
    {
      id: "1",
      name: "商品名稱...",
      price: 888,
      originalPrice: 999,
      image: "https://via.placeholder.com/200x150/E8E8E8/666666?text=商品圖片",
      sales: 10,
      isHot: false,
    },
    {
      id: "2",
      name: "一體成型球杆光滑結頭",
      price: 888,
      originalPrice: 999,
      image: "https://via.placeholder.com/200x150/4CAF50/FFFFFF?text=球桌",
      sales: 10,
      isHot: true,
    },
    {
      id: "3",
      name: "商品名稱...",
      price: 888,
      originalPrice: 999,
      image:
        "https://via.placeholder.com/200x150/9C27B0/FFFFFF?text=職業巧克粉",
      sales: 10,
      isHot: true,
    },
    {
      id: "4",
      name: "商品名稱...",
      price: 888,
      originalPrice: 999,
      image:
        "https://via.placeholder.com/200x150/2196F3/FFFFFF?text=職業巧克粉",
      sales: 10,
      isHot: false,
    },
    {
      id: "5",
      name: "專業撞球杆",
      price: 1299,
      originalPrice: 1599,
      image:
        "https://via.placeholder.com/200x150/FF9800/FFFFFF?text=專業撞球杆",
      sales: 25,
      isHot: true,
    },
    {
      id: "6",
      name: "高級巧克粉套裝",
      price: 299,
      originalPrice: 399,
      image:
        "https://via.placeholder.com/200x150/607D8B/FFFFFF?text=巧克粉套裝",
      sales: 8,
      isHot: false,
    },
  ];

  useEffect(() => {
    loadProducts();
  }, [selectedCategory]);

  const loadProducts = async () => {
    try {
      // 這裡可以實現實際的API調用
      console.log("Loading products for category:", selectedCategory);
    } catch (error) {
      console.error("加載商品失敗:", error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadProducts();
    setRefreshing(false);
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleMenu = () => {
    console.log("Menu pressed");
  };

  const handleSearch = () => {
    navigation.navigate("ProductSearch" as never, { query: searchQuery });
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  const handleProductPress = (productId: string) => {
    navigation.navigate("ProductDetail" as never, { productId });
  };

  const handleFavorite = (productId: string, isFavorite: boolean) => {
    console.log(`Product ${productId} favorite status: ${isFavorite}`);
  };

  const renderProduct = ({ item, index }: { item: any; index: number }) => (
    <View
      style={[styles.productWrapper, index % 2 === 1 && styles.rightProduct]}
    >
      <MallProductCard
        product={item}
        onPress={() => handleProductPress(item.id)}
        onFavorite={handleFavorite}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 頂部導航欄 */}
      <MallHeader onBack={handleBack} onMenu={handleMenu} />

      {/* 搜索欄 */}
      <MallSearchBar
        placeholder="搜索商品、教練"
        onPress={handleSearch}
        editable={false}
      />

      {/* 分類標籤 */}
      <CategoryTabs
        categories={categories}
        selectedCategory={selectedCategory}
        onCategorySelect={handleCategorySelect}
      />

      {/* 商品列表 */}
      <FlatList
        data={mockProducts}
        renderItem={renderProduct}
        numColumns={2}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.productList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  productList: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16, // 減少底部間距，Tab導航器會自動處理
  },
  productWrapper: {
    flex: 1,
    paddingRight: 8,
  },
  rightProduct: {
    paddingRight: 0,
    paddingLeft: 8,
  },
});

export default MallScreen;
