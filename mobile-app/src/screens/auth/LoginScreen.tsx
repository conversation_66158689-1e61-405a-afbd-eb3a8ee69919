import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { useDispatch } from "react-redux";
import { authAPI } from "../../services/api";
import { login } from "../../store/slices/authSlice";

interface LoginScreenProps {
  navigation: any;
}

export default function LoginScreen({ navigation }: LoginScreenProps) {
  const [phone, setPhone] = useState("");
  const [code, setCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [codeLoading, setCodeLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);

  const dispatch = useDispatch();

  // 验证手机号格式
  const validatePhone = (phoneNumber: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phoneNumber);
  };

  // 发送验证码
  const handleSendCode = async () => {
    if (!validatePhone(phone)) {
      Alert.alert("错误", "请输入正确的手机号");
      return;
    }

    if (countdown > 0) {
      return;
    }

    setCodeLoading(true);

    try {
      await authAPI.sendCode(phone);
      Alert.alert("成功", "验证码已发送");

      // 开始倒计时
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error: any) {
      Alert.alert("错误", error.message || "发送验证码失败");
    } finally {
      setCodeLoading(false);
    }
  };

  // 登录
  const handleLogin = async () => {
    if (!validatePhone(phone)) {
      Alert.alert("错误", "请输入正确的手机号");
      return;
    }

    if (!code || code.length < 4) {
      Alert.alert("错误", "请输入验证码");
      return;
    }

    setLoading(true);

    try {
      const response = await authAPI.login(phone, code);

      if (response.success) {
        // 更新Redux状态
        dispatch(
          login({
            user: response.data.user,
            token: response.data.token,
          }),
        );

        Alert.alert("成功", "登录成功");
      }
    } catch (error: any) {
      Alert.alert("错误", error.message || "登录失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <View style={styles.content}>
        <Text style={styles.title}>欢迎使用 Shuan-Q</Text>
        <Text style={styles.subtitle}>台球教练预约平台</Text>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>手机号</Text>
            <TextInput
              style={styles.input}
              placeholder="请输入手机号"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              maxLength={11}
              editable={!loading}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>验证码</Text>
            <View style={styles.codeInputContainer}>
              <TextInput
                style={[styles.input, styles.codeInput]}
                placeholder="请输入验证码"
                value={code}
                onChangeText={setCode}
                keyboardType="number-pad"
                maxLength={6}
                editable={!loading}
              />

              <TouchableOpacity
                style={[
                  styles.codeButton,
                  (countdown > 0 || codeLoading) && styles.codeButtonDisabled,
                ]}
                onPress={handleSendCode}
                disabled={countdown > 0 || codeLoading}
              >
                {codeLoading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.codeButtonText}>
                    {countdown > 0 ? `${countdown}s` : "获取验证码"}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>

          <TouchableOpacity
            style={[styles.loginButton, loading && styles.loginButtonDisabled]}
            onPress={handleLogin}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.loginButtonText}>登录</Text>
            )}
          </TouchableOpacity>

          <Text style={styles.agreement}>
            登录即表示同意
            <Text style={styles.agreementLink}> 用户协议 </Text>和
            <Text style={styles.agreementLink}> 隐私政策</Text>
          </Text>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: "center",
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    textAlign: "center",
    color: "#5a9178",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: "center",
    color: "#666",
    marginBottom: 40,
  },
  form: {
    marginTop: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  codeInputContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  codeInput: {
    flex: 1,
    marginRight: 10,
  },
  codeButton: {
    backgroundColor: "#5a9178",
    paddingHorizontal: 16,
    paddingVertical: 15,
    borderRadius: 8,
    minWidth: 100,
    alignItems: "center",
  },
  codeButtonDisabled: {
    backgroundColor: "#ccc",
  },
  codeButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "500",
  },
  loginButton: {
    backgroundColor: "#5a9178",
    padding: 15,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 10,
  },
  loginButtonDisabled: {
    backgroundColor: "#ccc",
  },
  loginButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
  },
  agreement: {
    textAlign: "center",
    fontSize: 12,
    color: "#666",
    marginTop: 20,
    lineHeight: 18,
  },
  agreementLink: {
    color: "#5a9178",
    textDecorationLine: "underline",
  },
});
