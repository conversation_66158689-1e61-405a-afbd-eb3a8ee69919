import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "../../store";
import { fetchPackageDetail } from "../../store/slices/packageSlice";
import { createOrder } from "../../store/slices/orderSlice";

interface PackageDetailScreenProps {
  route: {
    params: {
      packageId: string;
    };
  };
  navigation: any;
}

const PackageDetailScreen: React.FC<PackageDetailScreenProps> = ({
  route,
  navigation,
}) => {
  const { packageId } = route.params;
  const dispatch = useDispatch<AppDispatch>();
  const { currentPackage, loading } = useSelector(
    (state: RootState) => state.packages,
  );
  const { loading: orderLoading } = useSelector(
    (state: RootState) => state.orders,
  );
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  const [purchasing, setPurchasing] = useState(false);

  useEffect(() => {
    loadPackageDetail();
  }, [packageId]);

  const loadPackageDetail = async () => {
    try {
      await dispatch(fetchPackageDetail(packageId)).unwrap();
    } catch (err) {
      console.error("加载课程包详情失败:", err);
      Alert.alert("错误", "加载课程包详情失败");
    }
  };

  const handlePurchase = async () => {
    if (!isAuthenticated) {
      Alert.alert("提示", "请先登录", [
        { text: "取消", style: "cancel" },
        { text: "去登录", onPress: () => navigation.navigate("Login") },
      ]);
      return;
    }

    if (!currentPackage) return;

    Alert.alert(
      "确认购买",
      `确定要购买"${currentPackage.name}"吗？\n价格：¥${currentPackage.price}`,
      [
        { text: "取消", style: "cancel" },
        { text: "确认", onPress: confirmPurchase },
      ],
    );
  };

  const confirmPurchase = async () => {
    if (!currentPackage) return;

    setPurchasing(true);
    try {
      const order = await dispatch(
        createOrder({
          packageId: currentPackage.id,
          paymentMethod: "wechat",
        }),
      ).unwrap();

      Alert.alert(
        "订单创建成功",
        `订单号：${order.orderNumber}\n请在30分钟内完成支付`,
        [
          { text: "稍后支付", style: "cancel" },
          {
            text: "立即支付",
            onPress: () =>
              navigation.navigate("Payment", { orderId: order.id }),
          },
        ],
      );
    } catch (err) {
      Alert.alert("错误", err.message || "创建订单失败");
    } finally {
      setPurchasing(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#5a9178" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  if (!currentPackage) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>课程包不存在</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.retryText}>返回</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.packageName}>{currentPackage.name}</Text>
          <View style={styles.levelBadge}>
            <Text style={styles.levelText}>
              {currentPackage.level === "beginner"
                ? "初级"
                : currentPackage.level === "intermediate"
                  ? "中级"
                  : "高级"}
            </Text>
          </View>
        </View>

        <View style={styles.priceSection}>
          <View style={styles.priceContainer}>
            {currentPackage.originalPrice > currentPackage.price && (
              <Text style={styles.originalPrice}>
                ¥{currentPackage.originalPrice}
              </Text>
            )}
            <Text style={styles.price}>¥{currentPackage.price}</Text>
          </View>
          <Text style={styles.discount}>
            {currentPackage.originalPrice > currentPackage.price &&
              `节省 ¥${currentPackage.originalPrice - currentPackage.price}`}
          </Text>
        </View>

        <View style={styles.infoSection}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>课时数量</Text>
            <Text style={styles.infoValue}>
              {currentPackage.totalSessions} 课时
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>有效期</Text>
            <Text style={styles.infoValue}>
              {currentPackage.validityDays} 天
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>课程分类</Text>
            <Text style={styles.infoValue}>{currentPackage.category}</Text>
          </View>
        </View>

        {currentPackage.coach && (
          <View style={styles.coachSection}>
            <Text style={styles.sectionTitle}>教练信息</Text>
            <View style={styles.coachCard}>
              <Text style={styles.coachName}>
                {currentPackage.coach.user?.nickname}
              </Text>
              <Text style={styles.coachRating}>
                评分: {currentPackage.coach.rating}⭐
              </Text>
            </View>
          </View>
        )}

        <View style={styles.descriptionSection}>
          <Text style={styles.sectionTitle}>课程介绍</Text>
          <Text style={styles.description}>{currentPackage.description}</Text>
        </View>

        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>课程特色</Text>
          {currentPackage.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Text style={styles.featureBullet}>✓</Text>
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.purchaseButton,
            (purchasing || orderLoading) && styles.purchaseButtonDisabled,
          ]}
          onPress={handlePurchase}
          disabled={purchasing || orderLoading}
        >
          {purchasing || orderLoading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.purchaseButtonText}>立即购买</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  content: {
    flex: 1,
  },
  header: {
    backgroundColor: "#fff",
    padding: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  packageName: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    flex: 1,
    marginRight: 12,
  },
  levelBadge: {
    backgroundColor: "#5a9178",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  levelText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "500",
  },
  priceSection: {
    backgroundColor: "#fff",
    padding: 20,
    marginTop: 8,
    alignItems: "center",
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  originalPrice: {
    fontSize: 16,
    color: "#999",
    textDecorationLine: "line-through",
    marginRight: 12,
  },
  price: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#e74c3c",
  },
  discount: {
    fontSize: 14,
    color: "#27ae60",
    fontWeight: "500",
  },
  infoSection: {
    backgroundColor: "#fff",
    padding: 20,
    marginTop: 8,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  infoLabel: {
    fontSize: 16,
    color: "#666",
  },
  infoValue: {
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
  },
  coachSection: {
    backgroundColor: "#fff",
    padding: 20,
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 12,
  },
  coachCard: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
  },
  coachName: {
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
  },
  coachRating: {
    fontSize: 14,
    color: "#ffc107",
  },
  descriptionSection: {
    backgroundColor: "#fff",
    padding: 20,
    marginTop: 8,
  },
  description: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
  },
  featuresSection: {
    backgroundColor: "#fff",
    padding: 20,
    marginTop: 8,
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  featureBullet: {
    fontSize: 16,
    color: "#5a9178",
    marginRight: 12,
    fontWeight: "bold",
  },
  featureText: {
    fontSize: 16,
    color: "#333",
    flex: 1,
  },
  footer: {
    backgroundColor: "#fff",
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: "#e9ecef",
  },
  purchaseButton: {
    backgroundColor: "#5a9178",
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  purchaseButtonDisabled: {
    backgroundColor: "#ccc",
  },
  purchaseButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#666",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 16,
    color: "#666",
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: "#5a9178",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "500",
  },
});

export default PackageDetailScreen;
