import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  FlatList,
} from "react-native";
import { useRoute, useNavigation } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import Icon from "react-native-vector-icons/MaterialIcons";
import { RootState } from "../../store";
import { fetchCoachById } from "../../store/slices/coachSlice";
import { fetchPackages } from "../../store/slices/packageSlice";

// 导入组件
import ProductCard from "../../components/ProductCard";

const CoachDetailScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const { coachId } = route.params as any;
  const [activeTab, setActiveTab] = useState("info");

  const { selectedCoach, loading } = useSelector(
    (state: RootState) => state.coach,
  );
  const { packages } = useSelector((state: RootState) => state.packages);
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (coachId) {
      loadCoachDetail();
      loadCoachPackages();
    }
  }, [coachId]);

  const loadCoachDetail = async () => {
    try {
      await dispatch(fetchCoachById(coachId) as any);
    } catch (error) {
      Alert.alert("错误", "加载教练详情失败");
    }
  };

  const loadCoachPackages = async () => {
    try {
      await dispatch(fetchPackages({ coachId, limit: 10 }) as any);
    } catch (error) {
      console.error("加载教练课程包失败:", error);
    }
  };

  const handleBookAppointment = () => {
    if (!isAuthenticated) {
      Alert.alert("提示", "请先登录", [
        { text: "取消", style: "cancel" },
        {
          text: "去登录",
          onPress: () => navigation.navigate("Login" as never),
        },
      ]);
      return;
    }

    navigation.navigate("AppointmentCreate" as never, { coachId });
  };

  const handlePackagePress = (packageId: string) => {
    navigation.navigate("PackageDetail" as never, { packageId });
  };

  const renderPackageItem = ({ item }: { item: any }) => (
    <ProductCard package={item} onPress={() => handlePackagePress(item.id)} />
  );

  if (loading || !selectedCoach) {
    return (
      <View style={styles.loadingContainer}>
        <Text>加载中...</Text>
      </View>
    );
  }

  const coachPackages = packages.filter((pkg) => pkg.coach?.id === coachId);

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 教练头像和基本信息 */}
        <View style={styles.headerContainer}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <Icon name="person" size={48} color="#5a9178" />
            </View>
            <View style={styles.ratingContainer}>
              <Icon name="star" size={16} color="#faad14" />
              <Text style={styles.rating}>{selectedCoach.rating || 5.0}</Text>
            </View>
          </View>

          <View style={styles.basicInfo}>
            <Text style={styles.coachName}>
              {selectedCoach.user?.nickname || "未知教练"}
            </Text>
            <Text style={styles.experience}>
              {selectedCoach.experience || 0}年教学经验
            </Text>
            <Text style={styles.hourlyRate}>
              ¥{selectedCoach.hourlyRate || 100}/小时
            </Text>
          </View>
        </View>

        {/* 专长标签 */}
        {selectedCoach.specialties && selectedCoach.specialties.length > 0 && (
          <View style={styles.specialtiesContainer}>
            <Text style={styles.sectionTitle}>专长领域</Text>
            <View style={styles.tagsContainer}>
              {selectedCoach.specialties.map((specialty, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{specialty}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Tab切换 */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "info" && styles.activeTab]}
            onPress={() => setActiveTab("info")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "info" && styles.activeTabText,
              ]}
            >
              教练介绍
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === "packages" && styles.activeTab]}
            onPress={() => setActiveTab("packages")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "packages" && styles.activeTabText,
              ]}
            >
              课程包 ({coachPackages.length})
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tab内容 */}
        {activeTab === "info" ? (
          <View style={styles.infoContent}>
            <Text style={styles.bio}>
              {selectedCoach.bio || "这位教练还没有添加个人介绍。"}
            </Text>

            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Icon name="school" size={24} color="#5a9178" />

                <Text style={styles.statLabel}>教学经验</Text>
                <Text style={styles.statValue}>
                  {selectedCoach.experience || 0}年
                </Text>
              </View>
              <View style={styles.statItem}>
                <Icon name="star" size={24} color="#faad14" />

                <Text style={styles.statLabel}>用户评分</Text>
                <Text style={styles.statValue}>
                  {selectedCoach.rating || 5.0}
                </Text>
              </View>
              <View style={styles.statItem}>
                <Icon name="people" size={24} color="#52c41a" />

                <Text style={styles.statLabel}>学员数量</Text>
                <Text style={styles.statValue}>
                  {selectedCoach.studentCount || 0}
                </Text>
              </View>
            </View>
          </View>
        ) : (
          <View style={styles.packagesContent}>
            {coachPackages.length > 0 ? (
              <FlatList
                data={coachPackages}
                renderItem={renderPackageItem}
                keyExtractor={(item) => item.id}
                numColumns={2}
                scrollEnabled={false}
                contentContainerStyle={styles.packageGrid}
              />
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>该教练暂无课程包</Text>
              </View>
            )}
          </View>
        )}
      </ScrollView>

      {/* 底部操作栏 */}
      <View style={styles.bottomBar}>
        <TouchableOpacity
          style={styles.bookButton}
          onPress={handleBookAppointment}
        >
          <Icon name="event" size={20} color="#fff" />
          <Text style={styles.bookButtonText}>预约课程</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  scrollView: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: "row",
    padding: 20,
    backgroundColor: "#f8f9fa",
  },
  avatarContainer: {
    alignItems: "center",
    marginRight: 20,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#f0f8f5",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  rating: {
    fontSize: 14,
    color: "#333",
    marginLeft: 4,
    fontWeight: "600",
  },
  basicInfo: {
    flex: 1,
    justifyContent: "center",
  },
  coachName: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  experience: {
    fontSize: 16,
    color: "#666",
    marginBottom: 8,
  },
  hourlyRate: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#f5222d",
  },
  specialtiesContainer: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#e1e1e1",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  tag: {
    backgroundColor: "#f0f8f5",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 14,
    color: "#5a9178",
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: "#f8f9fa",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e1e1",
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: "center",
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#5a9178",
  },
  tabText: {
    fontSize: 16,
    color: "#666",
  },
  activeTabText: {
    color: "#5a9178",
    fontWeight: "600",
  },
  infoContent: {
    padding: 20,
  },
  bio: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
    marginBottom: 24,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    marginTop: 8,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
  },
  packagesContent: {
    padding: 20,
  },
  packageGrid: {
    paddingBottom: 16,
  },
  emptyContainer: {
    alignItems: "center",
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: "#999",
  },
  bottomBar: {
    padding: 20,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#e1e1e1",
  },
  bookButton: {
    backgroundColor: "#5a9178",
    paddingVertical: 16,
    borderRadius: 12,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  bookButtonText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
    marginLeft: 8,
  },
});

export default CoachDetailScreen;
