import React, { useState } from "react";
import { View, StyleSheet, FlatList, RefreshControl } from "react-native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RouteProp } from "@react-navigation/native";
import { HomeStackParamList } from "../../navigation/types";
import CoachHeader from "../../components/CoachHeader";
import CoachFilterTabs from "../../components/CoachFilterTabs";
import CoachCard from "../../components/CoachCard";

type CoachListScreenNavigationProp = NativeStackNavigationProp<
  HomeStackParamList,
  "CoachList"
>;

type CoachListScreenRouteProp = RouteProp<HomeStackParamList, "CoachList">;

interface Props {
  navigation: CoachListScreenNavigationProp;
  route: CoachListScreenRouteProp;
}

const CoachListScreen: React.FC<Props> = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);

  // Filter categories
  const filterCategories = [
    { label: "级别", icon: "star-border" },
    { label: "种类", icon: "category" },
    { label: "省份", icon: "place" },
    { label: "性别", icon: "person-outline" },
  ];

  // Mock coach data
  const coaches = [
    {
      id: "1",
      user: { nickname: "張偉教練" },
      experience: 8,
      rating: 4.9,
      specialties: ["基礎教學", "技巧提升", "比賽指導"],
      bio: "專業台球教練，擅長基礎教學和技巧提升，已培養多名專業選手。",
    },
    {
      id: "2",
      user: { nickname: "李明教練" },
      experience: 6,
      rating: 4.8,
      specialties: ["進階技巧", "戰術分析"],
      bio: "技術全面，戰術分析能力強，善於幫助學員突破技術瓶頸。",
    },
    {
      id: "3",
      user: { nickname: "王芳教練" },
      experience: 4,
      rating: 4.7,
      specialties: ["女性教學", "基礎入門"],
      bio: "女性教練，溫柔耐心，特別��長女性學員的基礎教學。",
    },
    {
      id: "4",
      user: { nickname: "陳强教練" },
      experience: 10,
      rating: 5.0,
      specialties: ["競賽培訓", "專業選手指導"],
      bio: "資深教練，曾培養多名全國冠軍，專業競賽培訓經驗豐富。",
    },
    {
      id: "5",
      user: { nickname: "劉濤教練" },
      experience: 5,
      rating: 4.6,
      specialties: ["青少年教學", "基礎糾正"],
      bio: "專注青少年台球教學，耐心細心，深受家長和學員喜愛。",
    },
    {
      id: "6",
      user: { nickname: "趙敏教練" },
      experience: 3,
      rating: 4.5,
      specialties: ["入門教學", "興趣培養"],
      bio: "年輕有活力，善於激發學員興趣，讓學習台球變得有趣。",
    },
  ];

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleSearch = () => {
    navigation.navigate("CoachSearch");
  };

  const handleFilter = () => {
    // Handle filter modal
  };
  const handleCategoryChange = (category: string) => {
    console.log("Selected category:", category);
    // Handle filter logic here
  };

  const handleCoachPress = (coachId: string) => {
    navigation.navigate("CoachDetail", { coachId });
  };

  const renderCoach = ({ item }: { item: any }) => (
    <View style={styles.cardWrapper}>
      <CoachCard coach={item} onPress={() => handleCoachPress(item.id)} />
    </View>
  );

  return (
    <View style={styles.container}>
      <CoachHeader
        onBack={handleBack}
        onSearch={handleSearch}
        onFilter={handleFilter}
      />

      <CoachFilterTabs
        categories={filterCategories}
        onCategoryChange={handleCategoryChange}
      />

      <FlatList
        data={coaches}
        renderItem={renderCoach}
        keyExtractor={(item) => item.id}
        numColumns={2}
        style={styles.coachList}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  coachList: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 8,
    paddingBottom: 16,
  },
  cardWrapper: {
    flex: 1 / 2,
    padding: 8,
  },
});

export default CoachListScreen;
