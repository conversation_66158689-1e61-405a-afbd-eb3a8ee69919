import React, { useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
} from "react-native";
import { useRoute, useNavigation } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import Icon from "react-native-vector-icons/MaterialIcons";
import { RootState } from "../../store";
import { fetchPackageById } from "../../store/slices/packageSlice";
import { createOrder } from "../../store/slices/orderSlice";

const ProductDetailScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const { productId } = route.params as any;
  const { currentPackage, loading } = useSelector(
    (state: RootState) => state.packages,
  );
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (productId) {
      dispatch(fetchPackageById(productId) as any);
    }
  }, [productId]);

  const handleBuyNow = async () => {
    if (!isAuthenticated) {
      Alert.alert("提示", "请先登录", [
        { text: "取消", style: "cancel" },
        {
          text: "去登录",
          onPress: () => navigation.navigate("Login" as never),
        },
      ]);
      return;
    }

    if (!currentPackage) return;

    try {
      const result = await dispatch(
        createOrder({
          packageId: currentPackage.id,
          paymentMethod: "wechat",
        }) as any,
      );

      if (result.payload) {
        Alert.alert("成功", "订单创建成功", [
          {
            text: "查看订单",
            onPress: () =>
              navigation.navigate("OrderDetail" as never, {
                orderId: result.payload.id,
              }),
          },
          { text: "继续购物", style: "cancel" },
        ]);
      }
    } catch (error: any) {
      Alert.alert("错误", error.message || "创建订单失败");
    }
  };

  const handleContactCoach = () => {
    if (!currentPackage?.coach) return;

    navigation.navigate("CoachDetail" as never, {
      coachId: currentPackage.coach.id,
    });
  };

  if (loading || !currentPackage) {
    return (
      <View style={styles.loadingContainer}>
        <Text>加载中...</Text>
      </View>
    );
  }

  const getLevelText = (level: string) => {
    switch (level) {
      case "beginner":
        return "初级";
      case "intermediate":
        return "中级";
      case "advanced":
        return "高级";
      default:
        return level;
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 商品图片 */}
        <View style={styles.imageContainer}>
          <Image
            source={{
              uri: `https://via.placeholder.com/400x300/5a9178/ffffff?text=${encodeURIComponent(currentPackage.name)}`,
            }}
            style={styles.productImage}
          />

          <View style={styles.levelBadge}>
            <Text style={styles.levelText}>
              {getLevelText(currentPackage.level)}
            </Text>
          </View>
        </View>

        {/* 商品信息 */}
        <View style={styles.infoContainer}>
          <Text style={styles.productName}>{currentPackage.name}</Text>

          <View style={styles.priceContainer}>
            <Text style={styles.price}>¥{currentPackage.price}</Text>
            {currentPackage.originalPrice &&
              currentPackage.originalPrice > currentPackage.price && (
                <Text style={styles.originalPrice}>
                  ¥{currentPackage.originalPrice}
                </Text>
              )}
          </View>

          <View style={styles.tagsContainer}>
            <View style={styles.tag}>
              <Text style={styles.tagText}>{currentPackage.category}</Text>
            </View>
            <View style={styles.tag}>
              <Text style={styles.tagText}>
                {currentPackage.totalSessions}课时
              </Text>
            </View>
            <View style={styles.tag}>
              <Text style={styles.tagText}>
                {currentPackage.validityDays}天有效
              </Text>
            </View>
          </View>

          <Text style={styles.description}>{currentPackage.description}</Text>
        </View>

        {/* 教练信息 */}
        {currentPackage.coach && (
          <TouchableOpacity
            style={styles.coachContainer}
            onPress={handleContactCoach}
          >
            <View style={styles.coachInfo}>
              <View style={styles.coachAvatar}>
                <Icon name="person" size={24} color="#5a9178" />
              </View>
              <View style={styles.coachDetails}>
                <Text style={styles.coachName}>
                  {currentPackage.coach.user?.nickname}
                </Text>
                <Text style={styles.coachTitle}>专业教练</Text>
              </View>
            </View>
            <Icon name="chevron-right" size={24} color="#999" />
          </TouchableOpacity>
        )}

        {/* 课程详情 */}
        <View style={styles.detailsContainer}>
          <Text style={styles.detailsTitle}>课程详情</Text>
          <View style={styles.detailItem}>
            <Icon name="schedule" size={20} color="#666" />
            <Text style={styles.detailText}>
              总课时：{currentPackage.totalSessions}节
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Icon name="access-time" size={20} color="#666" />

            <Text style={styles.detailText}>
              有效期：{currentPackage.validityDays}天
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Icon name="star" size={20} color="#666" />
            <Text style={styles.detailText}>
              难度级别：{getLevelText(currentPackage.level)}
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* 底部操作栏 */}
      <View style={styles.bottomBar}>
        <TouchableOpacity style={styles.buyButton} onPress={handleBuyNow}>
          <Text style={styles.buyButtonText}>立即购买</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: "relative",
  },
  productImage: {
    width: "100%",
    height: 300,
    resizeMode: "cover",
  },
  levelBadge: {
    position: "absolute",
    top: 16,
    right: 16,
    backgroundColor: "#5a9178",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  levelText: {
    fontSize: 12,
    color: "#fff",
    fontWeight: "600",
  },
  infoContainer: {
    padding: 20,
  },
  productName: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 12,
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  price: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#f5222d",
  },
  originalPrice: {
    fontSize: 18,
    color: "#999",
    textDecorationLine: "line-through",
    marginLeft: 12,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 16,
  },
  tag: {
    backgroundColor: "#f0f8f5",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: "#5a9178",
  },
  description: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
  },
  coachContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#f8f9fa",
    marginHorizontal: 20,
    borderRadius: 12,
    marginBottom: 20,
  },
  coachInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  coachAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#f0f8f5",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  coachDetails: {
    flex: 1,
  },
  coachName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  coachTitle: {
    fontSize: 14,
    color: "#666",
    marginTop: 2,
  },
  detailsContainer: {
    padding: 20,
    backgroundColor: "#f8f9fa",
    margin: 20,
    borderRadius: 12,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 16,
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  detailText: {
    fontSize: 14,
    color: "#666",
    marginLeft: 8,
  },
  bottomBar: {
    padding: 20,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#e1e1e1",
  },
  buyButton: {
    backgroundColor: "#5a9178",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  buyButtonText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
  },
});

export default ProductDetailScreen;
