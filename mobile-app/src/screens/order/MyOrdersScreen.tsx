import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import Icon from "react-native-vector-icons/MaterialIcons";
import { RootState } from "../../store";
import { fetchMyOrders, payOrder } from "../../store/slices/orderSlice";

const MyOrdersScreen: React.FC = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("all");

  const { orders, loading } = useSelector((state: RootState) => state.orders);

  const statusTabs = [
    { key: "all", label: "全部" },
    { key: "pending", label: "待支付" },
    { key: "paid", label: "已支付" },
    { key: "cancelled", label: "已取消" },
  ];

  useEffect(() => {
    loadOrders();
  }, [selectedStatus]);

  const loadOrders = async () => {
    try {
      const params: any = {};
      if (selectedStatus !== "all") {
        params.status = selectedStatus;
      }
      await dispatch(fetchMyOrders(params) as any);
    } catch (error) {
      Alert.alert("错误", "加载订单失败");
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  const handlePayOrder = async (orderId: string) => {
    try {
      await dispatch(payOrder(orderId) as any);
      Alert.alert("成功", "支付成功");
      loadOrders();
    } catch (error: any) {
      Alert.alert("错误", error.message || "支付失败");
    }
  };

  const handleOrderPress = (orderId: string) => {
    navigation.navigate("OrderDetail" as never, { orderId });
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "待支付";
      case "paid":
        return "已支付";
      case "cancelled":
        return "已取消";
      case "refunded":
        return "已退款";
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "#faad14";
      case "paid":
        return "#52c41a";
      case "cancelled":
        return "#999";
      case "refunded":
        return "#f5222d";
      default:
        return "#666";
    }
  };

  const renderOrderItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.orderCard}
      onPress={() => handleOrderPress(item.id)}
    >
      <View style={styles.orderHeader}>
        <Text style={styles.orderNumber}>订单号: {item.orderNumber}</Text>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(item.status) },
          ]}
        >
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>

      <View style={styles.orderContent}>
        <View style={styles.packageInfo}>
          <Text style={styles.packageName}>
            {item.package?.name || "未知课程包"}
          </Text>
          <Text style={styles.packageDetails}>
            {item.package?.totalSessions}课时 · {item.package?.validityDays}
            天有效
          </Text>
        </View>
        <Text style={styles.orderAmount}>¥{item.amount}</Text>
      </View>

      <View style={styles.orderFooter}>
        <Text style={styles.orderTime}>
          {new Date(item.createdAt).toLocaleString()}
        </Text>
        {item.status === "pending" && (
          <TouchableOpacity
            style={styles.payButton}
            onPress={() => handlePayOrder(item.id)}
          >
            <Text style={styles.payButtonText}>立即支付</Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Icon name="shopping-bag" size={64} color="#ccc" />
      <Text style={styles.emptyText}>暂无订单</Text>
    </View>
  );

  const filteredOrders =
    selectedStatus === "all"
      ? orders
      : orders.filter((order) => order.status === selectedStatus);

  return (
    <View style={styles.container}>
      {/* 状态标签 */}
      <View style={styles.tabContainer}>
        {statusTabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[styles.tab, selectedStatus === tab.key && styles.activeTab]}
            onPress={() => setSelectedStatus(tab.key)}
          >
            <Text
              style={[
                styles.tabText,
                selectedStatus === tab.key && styles.activeTabText,
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* 订单列表 */}
      <FlatList
        data={filteredOrders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyComponent}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e1e1",
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: "center",
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#5a9178",
  },
  tabText: {
    fontSize: 16,
    color: "#666",
  },
  activeTabText: {
    color: "#5a9178",
    fontWeight: "600",
  },
  listContainer: {
    padding: 16,
    flexGrow: 1,
  },
  orderCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  orderHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  orderNumber: {
    fontSize: 14,
    color: "#666",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: "#fff",
    fontWeight: "600",
  },
  orderContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  packageInfo: {
    flex: 1,
  },
  packageName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  packageDetails: {
    fontSize: 14,
    color: "#666",
  },
  orderAmount: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#f5222d",
  },
  orderFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  orderTime: {
    fontSize: 12,
    color: "#999",
  },
  payButton: {
    backgroundColor: "#5a9178",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  payButtonText: {
    fontSize: 14,
    color: "#fff",
    fontWeight: "600",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 16,
    color: "#999",
    marginTop: 16,
  },
});

export default MyOrdersScreen;
