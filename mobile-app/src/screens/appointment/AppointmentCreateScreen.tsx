import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useRoute, useNavigation } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import Icon from "react-native-vector-icons/MaterialIcons";
import { RootState } from "../../store";

const AppointmentCreateScreen: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const { coachId, packageId } = route.params as any;
  const [selectedDate, setSelectedDate] = useState("");
  const [selectedTime, setSelectedTime] = useState("");
  const [notes, setNotes] = useState("");

  const { selectedCoach } = useSelector((state: RootState) => state.coach);
  const { currentPackage } = useSelector((state: RootState) => state.packages);

  // 模拟可用时间段
  const availableTimes = [
    "09:00",
    "10:00",
    "11:00",
    "14:00",
    "15:00",
    "16:00",
    "17:00",
    "18:00",
  ];

  // 模拟可用日期（接下来7天）
  const getAvailableDates = () => {
    const dates = [];
    for (let i = 1; i <= 7; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      dates.push({
        value: date.toISOString().split("T")[0],
        label: `${date.getMonth() + 1}月${date.getDate()}日`,
        weekday: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"][
          date.getDay()
        ],
      });
    }
    return dates;
  };

  const availableDates = getAvailableDates();

  const handleCreateAppointment = async () => {
    if (!selectedDate || !selectedTime) {
      Alert.alert("提示", "请选择预约日期和时间");
      return;
    }

    try {
      // 这里应该调用创建预约的API
      Alert.alert("成功", "预约创建成功", [
        { text: "确定", onPress: () => navigation.goBack() },
      ]);
    } catch (error: any) {
      Alert.alert("错误", error.message || "创建预约失败");
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* 教练信息 */}
        {selectedCoach && (
          <View style={styles.coachInfo}>
            <View style={styles.coachAvatar}>
              <Icon name="person" size={32} color="#5a9178" />
            </View>
            <View style={styles.coachDetails}>
              <Text style={styles.coachName}>
                {selectedCoach.user?.nickname}
              </Text>
              <Text style={styles.coachRate}>
                ¥{selectedCoach.hourlyRate}/小时
              </Text>
            </View>
          </View>
        )}

        {/* 课程包信息 */}
        {currentPackage && (
          <View style={styles.packageInfo}>
            <Text style={styles.sectionTitle}>课程包信息</Text>
            <Text style={styles.packageName}>{currentPackage.name}</Text>
            <Text style={styles.packageDetails}>
              {currentPackage.totalSessions}课时 · {currentPackage.validityDays}
              天有效
            </Text>
          </View>
        )}

        {/* 选择日期 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>选择日期</Text>
          <View style={styles.dateGrid}>
            {availableDates.map((date) => (
              <TouchableOpacity
                key={date.value}
                style={[
                  styles.dateItem,
                  selectedDate === date.value && styles.selectedDateItem,
                ]}
                onPress={() => setSelectedDate(date.value)}
              >
                <Text
                  style={[
                    styles.dateLabel,
                    selectedDate === date.value && styles.selectedDateLabel,
                  ]}
                >
                  {date.label}
                </Text>
                <Text
                  style={[
                    styles.weekdayLabel,
                    selectedDate === date.value && styles.selectedWeekdayLabel,
                  ]}
                >
                  {date.weekday}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 选择时间 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>选择时间</Text>
          <View style={styles.timeGrid}>
            {availableTimes.map((time) => (
              <TouchableOpacity
                key={time}
                style={[
                  styles.timeItem,
                  selectedTime === time && styles.selectedTimeItem,
                ]}
                onPress={() => setSelectedTime(time)}
              >
                <Text
                  style={[
                    styles.timeText,
                    selectedTime === time && styles.selectedTimeText,
                  ]}
                >
                  {time}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 预约说明 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>预约说明</Text>
          <View style={styles.notesContainer}>
            <Text style={styles.notesText}>
              • 请提前15分钟到达 • 如需取消请提前24小时通知 •
              迟到超过15分钟视为爽约
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* 底部确认按钮 */}
      <View style={styles.bottomBar}>
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryText}>
            {selectedDate &&
              selectedTime &&
              `${availableDates.find((d) => d.value === selectedDate)?.label} ${selectedTime}`}
          </Text>
          <Text style={styles.priceText}>
            ¥{selectedCoach?.hourlyRate || 100}
          </Text>
        </View>
        <TouchableOpacity
          style={[
            styles.confirmButton,
            (!selectedDate || !selectedTime) && styles.disabledButton,
          ]}
          onPress={handleCreateAppointment}
          disabled={!selectedDate || !selectedTime}
        >
          <Text style={styles.confirmButtonText}>确认预约</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  scrollView: {
    flex: 1,
  },
  coachInfo: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    padding: 16,
    marginBottom: 12,
  },
  coachAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#f0f8f5",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  coachDetails: {
    flex: 1,
  },
  coachName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 4,
  },
  coachRate: {
    fontSize: 16,
    color: "#f5222d",
    fontWeight: "600",
  },
  packageInfo: {
    backgroundColor: "#fff",
    padding: 16,
    marginBottom: 12,
  },
  packageName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  packageDetails: {
    fontSize: 14,
    color: "#666",
  },
  section: {
    backgroundColor: "#fff",
    padding: 16,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 16,
  },
  dateGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  dateItem: {
    flex: 1,
    minWidth: 80,
    backgroundColor: "#f8f9fa",
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#e1e1e1",
  },
  selectedDateItem: {
    backgroundColor: "#5a9178",
    borderColor: "#5a9178",
  },
  dateLabel: {
    fontSize: 14,
    color: "#333",
    fontWeight: "600",
  },
  selectedDateLabel: {
    color: "#fff",
  },
  weekdayLabel: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
  },
  selectedWeekdayLabel: {
    color: "#fff",
  },
  timeGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  timeItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e1e1e1",
  },
  selectedTimeItem: {
    backgroundColor: "#5a9178",
    borderColor: "#5a9178",
  },
  timeText: {
    fontSize: 14,
    color: "#333",
    fontWeight: "600",
  },
  selectedTimeText: {
    color: "#fff",
  },
  notesContainer: {
    backgroundColor: "#f8f9fa",
    padding: 16,
    borderRadius: 8,
  },
  notesText: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
  bottomBar: {
    backgroundColor: "#fff",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#e1e1e1",
  },
  summaryContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  summaryText: {
    fontSize: 16,
    color: "#333",
  },
  priceText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#f5222d",
  },
  confirmButton: {
    backgroundColor: "#5a9178",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  disabledButton: {
    backgroundColor: "#ccc",
  },
  confirmButtonText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
  },
});

export default AppointmentCreateScreen;
