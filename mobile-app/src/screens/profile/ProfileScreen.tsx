import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  RefreshControl,
  Switch,
} from "react-native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RouteProp } from "@react-navigation/native";
import { HomeStackParamList } from "../../navigation/types";
import ProfileHeader from "../../components/ProfileHeader";
import Icon from "react-native-vector-icons/MaterialIcons";

type ProfileScreenNavigationProp = NativeStackNavigationProp<
  HomeStackParamList,
  "Profile"
>;

type ProfileScreenRouteProp = RouteProp<HomeStackParamList, "Profile">;

interface Props {
  navigation: ProfileScreenNavigationProp;
  route: ProfileScreenRouteProp;
}

const ProfileScreen: React.FC<Props> = ({ navigation }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [notificationEnabled, setNotificationEnabled] = useState(true);

  // Mock user data
  const user = {
    id: "1",
    name: "張小明",
    avatar: require("../../../images/default/avatar.png"),
    phone: "138****8888",
    email: "<EMAIL>",
    level: "VIP會員",
    points: 2580,
    balance: 1680.5,
    memberDays: 365,
  };

  // Stats data
  const stats = [
    { label: "我的預約", value: "12", color: "#1890ff", icon: "event" },
    { label: "學習課程", value: "8", color: "#52c41a", icon: "school" },
    { label: "我的收藏", value: "25", color: "#fa8c16", icon: "favorite" },
    { label: "我的評價", value: "15", color: "#eb2f96", icon: "star" },
  ];

  // Feature modules
  const features = [
    {
      title: "我的訂單",
      items: [
        { label: "待付款", icon: "payment", color: "#fa8c16", count: 2 },
        { label: "待確認", icon: "schedule", color: "#1890ff", count: 1 },
        { label: "進行中", icon: "play-arrow", color: "#52c41a", count: 3 },
        { label: "已完成", icon: "done", color: "#722ed1", count: 18 },
      ],
    },
    {
      title: "我的學習",
      items: [
        { label: "學習記錄", icon: "history", color: "#1890ff" },
        { label: "學習計劃", icon: "assignment", color: "#52c41a" },
        { label: "練習報告", icon: "assessment", color: "#fa8c16" },
        { label: "技能證書", icon: "card-membership", color: "#722ed1" },
      ],
    },
  ];

  // Settings options
  const settings = [
    {
      label: "個人資料",
      icon: "person",
      action: () => navigation.navigate("EditProfile"),
    },
    { label: "安全設置", icon: "security", action: () => {} },
    { label: "隱私設置", icon: "privacy-tip", action: () => {} },
    { label: "消息設置", icon: "notifications", action: () => {} },
    {
      label: "意見反饋",
      icon: "feedback",
      action: () => navigation.navigate("Feedback"),
    },
    { label: "關於我們", icon: "info", action: () => {} },
    { label: "客服中心", icon: "support", action: () => {} },
  ];

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const handleSettings = () => {
    navigation.navigate("Settings");
  };

  const handleNotification = () => {
    // Handle notification
  };
  const handleStatPress = (stat: any) => {
    switch (stat.label) {
      case "我的預約":
        navigation.navigate("MyAppointments");
        break;
      case "學習課程":
        navigation.navigate("MyCourses");
        break;
      case "我的收藏":
        navigation.navigate("MyFavorites");
        break;
      case "我的評價":
        navigation.navigate("MyReviews");
        break;
      default:
        break;
    }
  };

  const handleFeaturePress = (item: any) => {
    switch (item.label) {
      case "待付款":
      case "待確認":
      case "進行中":
      case "已完成":
        navigation.navigate("MyOrders", { status: item.label });
        break;
      case "學習記錄":
      case "學習計劃":
      case "練習報告":
      case "技能證書":
        navigation.navigate("LearningCenter", { type: item.label });
        break;
      default:
        break;
    }
  };

  const renderUserInfo = () => (
    <View style={styles.userSection}>
      <View style={styles.userHeader}>
        <TouchableOpacity style={styles.avatarContainer}>
          <Image
            source={user.avatar}
            style={styles.avatar}
            resizeMode="cover"
          />

          <View style={styles.editAvatarOverlay}>
            <Icon name="camera-alt" size={16} color="#fff" />
          </View>
        </TouchableOpacity>

        <View style={styles.userInfo}>
          <Text style={styles.userName}>{user.name}</Text>
          <View style={styles.levelBadge}>
            <Text style={styles.levelText}>{user.level}</Text>
          </View>
          <Text style={styles.userPhone}>{user.phone}</Text>
        </View>

        <TouchableOpacity
          style={styles.editButton}
          onPress={() => navigation.navigate("EditProfile")}
        >
          <Icon name="edit" size={20} color="#722ed1" />
        </TouchableOpacity>
      </View>

      <View style={styles.balanceSection}>
        <View style={styles.balanceItem}>
          <Text style={styles.balanceValue}>¥{user.balance.toFixed(2)}</Text>
          <Text style={styles.balanceLabel}>賬戶餘額</Text>
        </View>
        <View style={styles.balanceItem}>
          <Text style={styles.balanceValue}>{user.points}</Text>
          <Text style={styles.balanceLabel}>積分</Text>
        </View>
        <View style={styles.balanceItem}>
          <Text style={styles.balanceValue}>{user.memberDays}</Text>
          <Text style={styles.balanceLabel}>會員天數</Text>
        </View>
      </View>
    </View>
  );

  const renderStats = () => (
    <View style={styles.section}>
      <View style={styles.statsContainer}>
        {stats.map((stat, index) => (
          <TouchableOpacity
            key={index}
            style={styles.statItem}
            onPress={() => handleStatPress(stat)}
          >
            <Icon name={stat.icon} size={24} color={stat.color} />

            <Text style={styles.statValue}>{stat.value}</Text>
            <Text style={styles.statLabel}>{stat.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderFeatures = () => (
    <View>
      {features.map((feature, featureIndex) => (
        <View key={featureIndex} style={styles.section}>
          <Text style={styles.sectionTitle}>{feature.title}</Text>
          <View style={styles.featureGrid}>
            {feature.items.map((item, itemIndex) => (
              <TouchableOpacity
                key={itemIndex}
                style={styles.featureItem}
                onPress={() => handleFeaturePress(item)}
              >
                <View
                  style={[styles.featureIcon, { backgroundColor: item.color }]}
                >
                  <Icon name={item.icon} size={20} color="#fff" />

                  {item.count && item.count > 0 && (
                    <View style={styles.countBadge}>
                      <Text style={styles.countText}>{item.count}</Text>
                    </View>
                  )}
                </View>
                <Text style={styles.featureLabel}>{item.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      ))}
    </View>
  );

  const renderSettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>設置</Text>
      <View style={styles.settingsContainer}>
        {settings.map((setting, index) => (
          <TouchableOpacity
            key={index}
            style={styles.settingItem}
            onPress={setting.action}
          >
            <View style={styles.settingLeft}>
              <Icon name={setting.icon} size={20} color="#666" />

              <Text style={styles.settingLabel}>{setting.label}</Text>
            </View>
            {setting.label === "消息設置" ? (
              <Switch
                value={notificationEnabled}
                onValueChange={setNotificationEnabled}
                trackColor={{ false: "#ddd", true: "#722ed1" }}
                thumbColor={notificationEnabled ? "#fff" : "#fff"}
              />
            ) : (
              <Icon name="chevron-right" size={20} color="#ccc" />
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <ProfileHeader
        onSettings={handleSettings}
        onNotification={handleNotification}
      />

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderUserInfo()}
        {renderStats()}
        {renderFeatures()}
        {renderSettings()}

        <TouchableOpacity style={styles.logoutButton}>
          <Text style={styles.logoutText}>退出登錄</Text>
        </TouchableOpacity>

        <View style={styles.bottomSpace} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  content: {
    flex: 1,
  },
  userSection: {
    backgroundColor: "#722ed1",
    paddingBottom: 20,
  },
  userHeader: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 16,
  },
  avatarContainer: {
    position: "relative",
    marginRight: 12,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    borderWidth: 3,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  editAvatarOverlay: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "#722ed1",
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#fff",
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 4,
  },
  levelBadge: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    alignSelf: "flex-start",
    marginBottom: 4,
  },
  levelText: {
    fontSize: 10,
    color: "#fff",
    fontWeight: "bold",
  },
  userPhone: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.8)",
  },
  editButton: {
    width: 36,
    height: 36,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  balanceSection: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingHorizontal: 16,
  },
  balanceItem: {
    alignItems: "center",
  },
  balanceValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 4,
  },
  balanceLabel: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.8)",
  },
  section: {
    backgroundColor: "#fff",
    marginTop: 12,
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statValue: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
  },
  featureGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  featureItem: {
    width: "22%",
    alignItems: "center",
    marginBottom: 16,
  },
  featureIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
    position: "relative",
  },
  countBadge: {
    position: "absolute",
    top: -4,
    right: -4,
    backgroundColor: "#ff4757",
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  countText: {
    fontSize: 10,
    color: "#fff",
    fontWeight: "bold",
  },
  featureLabel: {
    fontSize: 11,
    color: "#666",
    textAlign: "center",
  },
  settingsContainer: {
    backgroundColor: "#fff",
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  settingLabel: {
    fontSize: 14,
    color: "#333",
    marginLeft: 12,
  },
  logoutButton: {
    backgroundColor: "#fff",
    marginHorizontal: 16,
    marginTop: 20,
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  logoutText: {
    fontSize: 16,
    color: "#ff4757",
    fontWeight: "bold",
  },
  bottomSpace: {
    height: 20,
  },
});

export default ProfileScreen;
