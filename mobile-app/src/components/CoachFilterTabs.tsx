import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialIcons";

interface CoachFilterTabsProps {
  categories: { label: string; icon: string }[];
  onCategoryChange: (category: string) => void;
}

const CoachFilterTabs: React.FC<CoachFilterTabsProps> = ({
  categories,
  onCategoryChange,
}) => {
  return (
    <View style={styles.tabsWrapper}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.container}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.label}
            style={styles.tab}
            onPress={() => onCategoryChange(category.label)}
          >
            <Icon name={category.icon} size={20} color="#666" />

            <Text style={styles.tabText}>{category.label}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  tabsWrapper: {
    backgroundColor: "#fff",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  container: {
    paddingHorizontal: 16,
    justifyContent: "space-around",
    flexDirection: "row",
    flexGrow: 1,
  },
  tab: {
    alignItems: "center",
    paddingHorizontal: 12,
  },
  tabText: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
  },
});

export default CoachFilterTabs;
