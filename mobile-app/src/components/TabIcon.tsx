import React from "react";
import { View, StyleSheet } from "react-native";
import FlatIcon from "./FlatIcon";

interface TabIconProps {
  name: string;
  focused: boolean;
  color: string;
  size: number;
}

const TabIcon: React.FC<TabIconProps> = ({ name, focused, color, size }) => {
  return <FlatIcon name={name} size={size} color={color} focused={focused} />;
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    width: 32,
    height: 32,
  },
  focused: {
    transform: [{ scale: 1.1 }],
  },
});

export default TabIcon;
