import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Dimensions,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialIcons";

const { height } = Dimensions.get("window");

interface FilterModalProps {
  visible: boolean;
  filters: {
    level: string;
    priceRange: string;
    sortBy: string;
    sortOrder: string;
  };
  onApply: (filters: any) => void;
  onClose: () => void;
}

const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  filters,
  onApply,
  onClose,
}) => {
  const [tempFilters, setTempFilters] = useState(filters);

  const levelOptions = [
    { value: "all", label: "全部级别" },
    { value: "beginner", label: "初级" },
    { value: "intermediate", label: "中级" },
    { value: "advanced", label: "高级" },
  ];

  const priceRangeOptions = [
    { value: "all", label: "全部价格" },
    { value: "0-100", label: "¥100以下" },
    { value: "100-300", label: "¥100-300" },
    { value: "300-500", label: "¥300-500" },
    { value: "500+", label: "¥500以上" },
  ];

  const sortOptions = [
    { value: "createdAt-DESC", label: "最新发布" },
    { value: "price-ASC", label: "价格从低到高" },
    { value: "price-DESC", label: "价格从高到低" },
    { value: "totalSessions-DESC", label: "课时数最多" },
  ];

  const handleFilterChange = (key: string, value: string) => {
    if (key === "sort") {
      const [sortBy, sortOrder] = value.split("-");
      setTempFilters({
        ...tempFilters,
        sortBy,
        sortOrder,
      });
    } else {
      setTempFilters({
        ...tempFilters,
        [key]: value,
      });
    }
  };

  const handleReset = () => {
    setTempFilters({
      level: "all",
      priceRange: "all",
      sortBy: "createdAt",
      sortOrder: "DESC",
    });
  };

  const handleApply = () => {
    onApply(tempFilters);
  };

  const renderFilterSection = (
    title: string,
    options: { value: string; label: string }[],
    selectedValue: string,
    onSelect: (value: string) => void,
  ) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.optionsContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.optionItem,
              selectedValue === option.value && styles.selectedOption,
            ]}
            onPress={() => onSelect(option.value)}
          >
            <Text
              style={[
                styles.optionText,
                selectedValue === option.value && styles.selectedOptionText,
              ]}
            >
              {option.label}
            </Text>
            {selectedValue === option.value && (
              <Icon name="check" size={16} color="#5a9178" />
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <Icon name="close" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={styles.title}>筛选</Text>
            <TouchableOpacity onPress={handleReset}>
              <Text style={styles.resetText}>重置</Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
          >
            {renderFilterSection(
              "课程级别",
              levelOptions,
              tempFilters.level,
              (value) => handleFilterChange("level", value),
            )}

            {renderFilterSection(
              "价格区间",
              priceRangeOptions,
              tempFilters.priceRange,
              (value) => handleFilterChange("priceRange", value),
            )}

            {renderFilterSection(
              "排序方式",
              sortOptions,
              `${tempFilters.sortBy}-${tempFilters.sortOrder}`,
              (value) => handleFilterChange("sort", value),
            )}
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity style={styles.applyButton} onPress={handleApply}>
              <Text style={styles.applyButtonText}>应用筛选</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  container: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.8,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e1e1e1",
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  resetText: {
    fontSize: 16,
    color: "#5a9178",
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 12,
  },
  optionsContainer: {
    gap: 8,
  },
  optionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: "#f8f9fa",
  },
  selectedOption: {
    backgroundColor: "#f0f8f5",
    borderWidth: 1,
    borderColor: "#5a9178",
  },
  optionText: {
    fontSize: 14,
    color: "#333",
  },
  selectedOptionText: {
    color: "#5a9178",
    fontWeight: "600",
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: "#e1e1e1",
  },
  applyButton: {
    backgroundColor: "#5a9178",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
  },
});

export default FilterModal;
