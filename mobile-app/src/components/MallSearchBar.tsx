import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialIcons";

interface MallSearchBarProps {
  placeholder?: string;
  onPress?: () => void;
  onSearch?: (query: string) => void;
  editable?: boolean;
  value?: string;
}

const MallSearchBar: React.FC<MallSearchBarProps> = ({
  placeholder = "搜索商品、教練",
  onPress,
  onSearch,
  editable = true,
  value,
}) => {
  if (!editable) {
    return (
      <TouchableOpacity style={styles.container} onPress={onPress}>
        <View style={styles.searchBar}>
          <Icon
            name="search"
            size={20}
            color="#999"
            style={styles.searchIcon}
          />

          <Text style={styles.placeholder}>{placeholder}</Text>
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.searchBar}>
        <Icon name="search" size={20} color="#999" style={styles.searchIcon} />

        <TextInput
          style={styles.input}
          placeholder={placeholder}
          placeholderTextColor="#999"
          value={value}
          onChangeText={onSearch}
          returnKeyType="search"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#f8f9fa",
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchIcon: {
    marginRight: 12,
  },
  placeholder: {
    fontSize: 16,
    color: "#999",
    flex: 1,
  },
  input: {
    fontSize: 16,
    color: "#333",
    flex: 1,
    padding: 0,
  },
});

export default MallSearchBar;
