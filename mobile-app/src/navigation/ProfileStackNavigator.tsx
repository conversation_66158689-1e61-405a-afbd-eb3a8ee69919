import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { ProfileStackParamList } from "./types";

// 导入页面组件
import ProfileScreen from "../screens/profile/ProfileScreen";
import EditProfileScreen from "../screens/profile/EditProfileScreen";
import MyOrdersScreen from "../screens/order/MyOrdersScreen";
import MyAppointmentsScreen from "../screens/appointment/MyAppointmentsScreen";
import SettingsScreen from "../screens/profile/SettingsScreen";

// 教练专用页面
import CoachProfileScreen from "../screens/coach/CoachProfileScreen";
import ManagePackagesScreen from "../screens/coach/ManagePackagesScreen";
import ManageAppointmentsScreen from "../screens/coach/ManageAppointmentsScreen";
import StudentManagementScreen from "../screens/coach/StudentManagementScreen";
import CreatePackageScreen from "../screens/course/CreatePackageScreen";
import EditPackageScreen from "../screens/course/EditPackageScreen";

const Stack = createStackNavigator<ProfileStackParamList>();

const ProfileStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: "#5a9178",
        },
        headerTintColor: "#fff",
        headerTitleStyle: {
          fontWeight: "bold",
        },
      }}
    >
      <Stack.Screen
        name="ProfileMain"
        component={ProfileScreen}
        options={{
          title: "我的",
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{ title: "编辑资料" }}
      />

      <Stack.Screen
        name="MyOrders"
        component={MyOrdersScreen}
        options={{ title: "我的订单" }}
      />

      <Stack.Screen
        name="MyAppointments"
        component={MyAppointmentsScreen}
        options={{ title: "我的预约" }}
      />

      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{ title: "设置" }}
      />

      {/* 教练专用页面 */}
      <Stack.Screen
        name="CoachProfile"
        component={CoachProfileScreen}
        options={{ title: "教练资料" }}
      />

      <Stack.Screen
        name="ManagePackages"
        component={ManagePackagesScreen}
        options={{ title: "课程包管理" }}
      />

      <Stack.Screen
        name="ManageAppointments"
        component={ManageAppointmentsScreen}
        options={{ title: "预约管理" }}
      />

      <Stack.Screen
        name="StudentManagement"
        component={StudentManagementScreen}
        options={{ title: "学员管理" }}
      />

      <Stack.Screen
        name="CreatePackage"
        component={CreatePackageScreen}
        options={{ title: "创建课程包" }}
      />

      <Stack.Screen
        name="EditPackage"
        component={EditPackageScreen}
        options={{ title: "编辑课程包" }}
      />
    </Stack.Navigator>
  );
};

export default ProfileStackNavigator;
