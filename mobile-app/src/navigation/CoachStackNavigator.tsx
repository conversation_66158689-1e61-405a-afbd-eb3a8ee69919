import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { CoachStackParamList } from "./types";

// 导入页面组件
import CoachListScreen from "../screens/coach/CoachListScreen";
import CoachDetailScreen from "../screens/coach/CoachDetailScreen";
import CoachSearchScreen from "../screens/coach/CoachSearchScreen";
import PackageDetailScreen from "../screens/course/PackageDetailScreen";
import AppointmentCreateScreen from "../screens/appointment/AppointmentCreateScreen";

const Stack = createStackNavigator<CoachStackParamList>();

const CoachStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: "#5a9178",
        },
        headerTintColor: "#fff",
        headerTitleStyle: {
          fontWeight: "bold",
        },
      }}
    >
      <Stack.Screen
        name="CoachMain"
        component={CoachListScreen}
        options={{
          title: "教练",
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="CoachDetail"
        component={CoachDetailScreen}
        options={{ title: "教练详情" }}
      />

      <Stack.Screen
        name="CoachSearch"
        component={CoachSearchScreen}
        options={{ title: "搜索教练" }}
      />

      <Stack.Screen
        name="PackageDetail"
        component={PackageDetailScreen}
        options={{ title: "课程包详情" }}
      />

      <Stack.Screen
        name="AppointmentCreate"
        component={AppointmentCreateScreen}
        options={{ title: "预约课程" }}
      />
    </Stack.Navigator>
  );
};

export default CoachStackNavigator;
