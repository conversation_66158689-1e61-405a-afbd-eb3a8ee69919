import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { Provider } from "react-redux";
import { store } from "./store";
import { RootStackParamList } from "./navigation/types";

// 导入页面组件
import TabNavigator from "./navigation/TabNavigator";
import LoginScreen from "./screens/auth/LoginScreen";
import PackageDetailScreen from "./screens/course/PackageDetailScreen";
import CoachDetailScreen from "./screens/coach/CoachDetailScreen";
import AppointmentCreateScreen from "./screens/appointment/AppointmentCreateScreen";
import OrderDetailScreen from "./screens/order/OrderDetailScreen";

const Stack = createStackNavigator<RootStackParamList>();

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Main"
          screenOptions={{
            headerStyle: {
              backgroundColor: "#5a9178",
            },
            headerTintColor: "#fff",
            headerTitleStyle: {
              fontWeight: "bold",
            },
          }}
        >
          <Stack.Screen
            name="Main"
            component={TabNavigator}
            options={{ headerShown: false }}
          />

          <Stack.Screen
            name="Login"
            component={LoginScreen}
            options={{
              title: "登录",
              headerShown: false,
            }}
          />

          <Stack.Screen
            name="PackageDetail"
            component={PackageDetailScreen}
            options={{ title: "课程包详情" }}
          />

          <Stack.Screen
            name="CoachDetail"
            component={CoachDetailScreen}
            options={{ title: "教练详情" }}
          />

          <Stack.Screen
            name="AppointmentCreate"
            component={AppointmentCreateScreen}
            options={{ title: "预约课程" }}
          />

          <Stack.Screen
            name="OrderDetail"
            component={OrderDetailScreen}
            options={{ title: "订单详情" }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    </Provider>
  );
};

export default App;
