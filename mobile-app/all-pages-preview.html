<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQ台球教學 - 四大頁面預覽</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .phone-mockup {
            width: 280px;
            height: 580px;
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.4);
            margin: 0 auto;
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            border-radius: 18px;
            overflow: hidden;
            position: relative;
        }

        /* Common Styles */
        .header {
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }

        .header-buttons {
            display: flex;
            gap: 8px;
        }

        .header-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
        }

        .content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 70px;
        }

        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid #e1e1e1;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            font-size: 10px;
        }

        .tab-item.active {
            color: #1890ff;
        }

        .tab-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        /* Home Page */
        .home-header {
            background: linear-gradient(135deg, #5a9178 0%, #4a7c59 100%);
        }

        .home-search {
            background: #5a9178;
            padding: 0 16px 16px;
        }

        .search-bar {
            background: rgba(255,255,255,0.9);
            border-radius: 25px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            color: #999;
        }

        .banner {
            height: 160px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            position: relative;
            overflow: hidden;
        }

        .banner-content {
            position: absolute;
            bottom: 16px;
            left: 16px;
            color: white;
            font-size: 16px;
            font-weight: bold;
        }

        .quick-actions {
            background: white;
            margin-top: 12px;
            padding: 20px 16px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }

        .action-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
        }

        .action-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            color: white;
            font-weight: bold;
        }

        .action-text {
            font-size: 12px;
            color: #666;
        }

        /* Mall Page */
        .mall-header {
            background: #1890ff;
        }

        .category-tabs {
            background: white;
            padding: 16px;
            display: flex;
            justify-content: center;
        }

        .tabs-container {
            display: flex;
            gap: 40px;
        }

        .tab {
            padding: 8px 20px;
            border-radius: 20px;
            background: #f5f5f5;
            color: #666;
            text-decoration: none;
            min-width: 80px;
            text-align: center;
            font-size: 14px;
        }

        .tab.active {
            background: #1890ff;
            color: white;
        }

        .product-grid {
            padding: 16px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product-image {
            height: 100px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            position: relative;
        }

        .hot-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #ff4757;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
        }

        .product-info {
            padding: 12px;
        }

        .product-title {
            font-size: 13px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .product-price {
            color: #ff4757;
            font-size: 14px;
            font-weight: bold;
        }

        /* Coach Page */
        .coach-header {
            background: #1890ff;
        }

        .coach-list {
            padding: 16px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .coach-card {
            background: white;
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }
        .coach-price {
            display: none;
        }
        .coach-header-info {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
        }
.
.
.
                    <div class="category-tabs">
                        <div class="tabs-container">
                            <a href="#" class="tab active">级别</a>
                            <a href="#" class="tab">种类</a>
                            <a href="#" class="tab">省份</a>
                            <a href="#" class="tab">性别</a>
                        </div>
                    </div>
                    
                    <div class="content">
                        <div class="coach-list">
                            <div class="coach-card">
                                <div class="coach-header-info">
                                    <div class="coach-avatar"></div>
                                    <div class="coach-info">
                                        <div class="coach-name">張偉教練</div>
                                        <div class="coach-level">金牌教練</div>
                                        <div style="font-size: 12px; color: #666; margin-top: 4px;">8年教學經驗</div>
                                        <div style="font-size: 12px; color: #666;">⭐ 4.9 (256位學員)</div>
                                    </div>
                                </div>
                                <div style="font-size: 13px; color: #666; margin-bottom: 12px; flex-grow: 1;">專業台球教練，擅長基礎教學和技巧提升...</div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <button style="border: 1px solid #1890ff; background: none; color: #1890ff; padding: 6px 12px; border-radius: 20px; font-size: 12px;">諮詢</button>
                                    <button style="background: #1890ff; color: white; border: none; padding: 8px 18px; border-radius: 20px; font-size: 12px;">預約</button>
                                </div>
                            </div>
                            
                            <div class="coach-card">
                                <div class="coach-header-info">
                                    <div class="coach-avatar"></div>
                                    <div class="coach-info">
                                        <div class="coach-name">李明教練</div>
                                        <div class="coach-level" style="background: #52c41a;">高級教練</div>
                                        <div style="font-size: 12px; color: #666; margin-top: 4px;">6年教學經驗</div>
                                        <div style="font-size: 12px; color: #666;">⭐ 4.8 (189位學員)</div>
                                    </div>
                                </div>
                                <div style="font-size: 13px; color: #666; margin-bottom: 12px; flex-grow: 1;">技術全面，戰術分析能力強...</div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <button style="border: 1px solid #1890ff; background: none; color: #1890ff; padding: 6px 12px; border-radius: 20px; font-size: 12px;">諮詢</button>
                                    <button style="background: #1890ff; color: white; border: none; padding: 8px 18px; border-radius: 20px; font-size: 12px;">預約</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-bar">
                        <div class="tab-item">
                            <div class="tab-icon">🏠</div>
                            <div>首頁</div>
                        </div>
                        <div class="tab-item">
                            <div class="tab-icon">🛍️</div>
                            <div>商城</div>
                        </div>
                        <div class="tab-item active">
                            <div class="tab-icon">👨‍🏫</div>
                            <div>教練</div>
                        </div>
                        <div class="tab-item">
                            <div class="tab-icon">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                    
                    <div class="page-label">教練</div>
                </div>
            </div>

            <!-- Profile Page -->
            <div class="phone-mockup">
                <div class="screen">
                    <div class="header profile-header">
                        <span>我的</span>
                        <div class="header-buttons">
                            <span class="header-btn">🔔</span>
                            <span class="header-btn">⚙️</span>
                        </div>
                    </div>
                    
                    <div class="content">
                        <div class="user-section">
                            <div class="user-info-container">
                                <div class="user-avatar"></div>
                                <div class="user-details">
                                    <div class="user-name">張小明</div>
                                    <div class="user-level">VIP會員</div>
                                    <div style="font-size: 12px; opacity: 0.8;">138****8888</div>
                                </div>
                                <span style="background: rgba(255,255,255,0.2); padding: 8px; border-radius: 18px;">✏️</span>
                            </div>
                            
                            <div class="balance-section">
                                <div class="balance-item">
                                    <div class="balance-value">¥1680.50</div>
                                    <div class="balance-label">賬戶餘額</div>
                                </div>
                                <div class="balance-item">
                                    <div class="balance-value">2580</div>
                                    <div class="balance-label">積分</div>
                                </div>
                                <div class="balance-item">
                                    <div class="balance-value">365</div>
                                    <div class="balance-label">會員天數</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="feature-section">
                            <div class="feature-grid">
                                <div class="feature-item">
                                    <div class="feature-icon" style="background: #1890ff;">📅</div>
                                    <div class="feature-label">我的預約</div>
                                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-top: 4px;">12</div>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon" style="background: #52c41a;">🎓</div>
                                    <div class="feature-label">學習課程</div>
                                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-top: 4px;">8</div>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon" style="background: #fa8c16;">❤️</div>
                                    <div class="feature-label">我的收藏</div>
                                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-top: 4px;">25</div>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon" style="background: #eb2f96;">⭐</div>
                                    <div class="feature-label">我的評價</div>
                                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-top: 4px;">15</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="feature-section">
                            <div class="section-title">我的訂單</div>
                            <div class="feature-grid">
                                <div class="feature-item">
                                    <div class="feature-icon" style="background: #fa8c16; position: relative;">💳
                                        <span style="position: absolute; top: -4px; right: -4px; background: #ff4757; border-radius: 8px; color: white; font-size: 10px; padding: 2px 4px;">2</span>
                                    </div>
                                    <div class="feature-label">待付款</div>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon" style="background: #1890ff; position: relative;">⏰
                                        <span style="position: absolute; top: -4px; right: -4px; background: #ff4757; border-radius: 8px; color: white; font-size: 10px; padding: 2px 4px;">1</span>
                                    </div>
                                    <div class="feature-label">待確認</div>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon" style="background: #52c41a; position: relative;">▶️
                                        <span style="position: absolute; top: -4px; right: -4px; background: #ff4757; border-radius: 8px; color: white; font-size: 10px; padding: 2px 4px;">3</span>
                                    </div>
                                    <div class="feature-label">進行中</div>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon" style="background: #722ed1;">✅</div>
                                    <div class="feature-label">已完成</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-bar">
                        <div class="tab-item">
                            <div class="tab-icon">🏠</div>
                            <div>首頁</div>
                        </div>
                        <div class="tab-item">
                            <div class="tab-icon">🛍️</div>
                            <div>商城</div>
                        </div>
                        <div class="tab-item">
                            <div class="tab-icon">👨‍🏫</div>
                            <div>教練</div>
                        </div>
                        <div class="tab-item active">
                            <div class="tab-icon">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                    
                    <div class="page-label">我的</div>
                </div>
            </div>
        </div>

        <div style="text-align: center; color: white; font-size: 14px; margin-top: 20px;">
            <p>✨ 統一設計風格 | 🎨 現代化UI | 📱 完美適配</p>
            <p style="margin-top: 8px; opacity: 0.8;">四大核心頁面，一致的用戶體驗，專業的台球教學平台</p>
        </div>
    </div>
</body>
</html> 