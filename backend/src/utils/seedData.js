const { sequelize, User, Coach, CourseCategory, CoursePackage } = require('../models');

// 初始化数据库表结构
const initDatabase = async () => {
  try {
    console.log('🔄 开始初始化数据库...');

    // 同步数据库表结构
    await sequelize.sync({ force: false });
    console.log('✅ 数据库表结构同步完成');

    // 初始化课程分类
    await initCourseCategories();

    // 初始化测试数据
    await initTestData();

    console.log('🎉 数据库初始化完成');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    throw error;
  }
};

// 初始化课程分类数据
const initCourseCategories = async () => {
  try {
    const categories = [
      {
        name: '基础入门',
        description: '适合台球初学者的基础课程',
        icon: '🎱',
        sortOrder: 1
      },
      {
        name: '技巧提升',
        description: '提升台球技巧和战术的进阶课程',
        icon: '🎯',
        sortOrder: 2
      },
      {
        name: '专业训练',
        description: '专业级别的台球训练课程',
        icon: '🏆',
        sortOrder: 3
      },
      {
        name: '比赛指导',
        description: '比赛技巧和心理素质训练',
        icon: '🥇',
        sortOrder: 4
      },
      {
        name: '私人定制',
        description: '根据个人需求定制的专属课程',
        icon: '⭐',
        sortOrder: 5
      }
    ];

    for (const categoryData of categories) {
      const [category, created] = await CourseCategory.findOrCreate({
        where: { name: categoryData.name },
        defaults: categoryData
      });

      if (created) {
        console.log(`✅ 创建课程分类: ${category.name}`);
      } else {
        console.log(`📋 课程分类已存在: ${category.name}`);
      }
    }

    console.log('🎉 课程分类初始化完成');
  } catch (error) {
    console.error('❌ 课程分类初始化失败:', error);
  }
};

// 初始化测试数据
const initTestData = async () => {
  try {
    console.log('🔄 开始初始化测试数据...');

    // 创建测试用户
    const [testUser, userCreated] = await User.findOrCreate({
      where: { phone: '13800138000' },
      defaults: {
        phone: '13800138000',
        nickname: '测试用户',
        avatar: 'https://via.placeholder.com/100',
        userType: 'student'
      }
    });

    if (userCreated) {
      console.log('✅ 创建测试用户: 测试用户');
    }

    // 创建测试教练用户
    const [coachUser, coachUserCreated] = await User.findOrCreate({
      where: { phone: '13800138001' },
      defaults: {
        phone: '13800138001',
        nickname: '张教练',
        avatar: 'https://via.placeholder.com/100',
        userType: 'coach'
      }
    });

    if (coachUserCreated) {
      console.log('✅ 创建测试教练用户: 张教练');
    }

    // 创建教练资料
    const [testCoach, coachCreated] = await Coach.findOrCreate({
      where: { userId: coachUser.id },
      defaults: {
        userId: coachUser.id,
        name: '张教练',
        specialties: ['基础教学', '技巧提升'],
        experience: 5,
        hourlyRate: 200,
        rating: 4.8,
        description: '专业台球教练，5年教学经验，擅长基础教学和技巧提升。',
        location: '北京市朝阳区',
        avatar: 'https://via.placeholder.com/150',
        isVerified: true,
        isActive: true
      }
    });

    if (coachCreated) {
      console.log('✅ 创建测试教练资料: 张教练');
    }

    // 创建测试课程包
    const categories = await CourseCategory.findAll();
    if (categories.length > 0) {
      const [testPackage, packageCreated] = await CoursePackage.findOrCreate({
        where: {
          coachId: testCoach.id,
          name: '台球基础入门课程包'
        },
        defaults: {
          coachId: testCoach.id,
          name: '台球基础入门课程包',
          description: '适合零基础学员的台球入门课程，包含基本姿势、瞄准技巧等。',
          price: 800,
          originalPrice: 1000,
          totalSessions: 10,
          level: 'beginner',
          category: categories[0].name,
          features: ['基础姿势教学', '瞄准技巧训练', '基本规则讲解'],
          isActive: true
        }
      });

      if (packageCreated) {
        console.log('✅ 创建测试课程包: 台球基础入门课程包');
      }
    }

    console.log('🎉 测试数据初始化完成');
  } catch (error) {
    console.error('❌ 测试数据初始化失败:', error);
  }
};

module.exports = {
  initDatabase,
  initCourseCategories,
  initTestData
};
