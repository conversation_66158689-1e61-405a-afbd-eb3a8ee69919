const express = require('express');
const router = express.Router();
const {
  getPackages,
  getPackageById,
  createPackage,
  updatePackage,
  getMyPackages
} = require('../controllers/coursePackageController');
const { authenticateToken } = require('../middleware/auth');
const { body, param, query } = require('express-validator');
const validate = require('../middleware/validate');

// 验证规则
const createPackageValidation = [
  body('name')
    .notEmpty()
    .withMessage('课程包名称不能为空')
    .isLength({ max: 100 })
    .withMessage('课程包名称不能超过100个字符'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('描述不能超过1000个字符'),
  body('totalSessions')
    .isInt({ min: 1, max: 100 })
    .withMessage('课时数必须是1-100之间的整数'),
  body('price')
    .isFloat({ min: 0 })
    .withMessage('价格必须是大于等于0的数字'),
  body('originalPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('原价必须是大于等于0的数字'),
  body('validityDays')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('有效期必须是1-365天之间的整数'),
  body('level')
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('级别必须是 beginner、intermediate 或 advanced'),
  body('category')
    .notEmpty()
    .withMessage('分类不能为空')
    .isLength({ max: 50 })
    .withMessage('分类不能超过50个字符'),
  body('features')
    .optional()
    .isArray()
    .withMessage('特色必须是数组格式')
];

const updatePackageValidation = [
  param('id')
    .isUUID()
    .withMessage('课程包ID格式不正确'),
  body('name')
    .optional()
    .notEmpty()
    .withMessage('课程包名称不能为空')
    .isLength({ max: 100 })
    .withMessage('课程包名称不能超过100个字符'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('描述不能超过1000个字符'),
  body('totalSessions')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('课时数必须是1-100之间的整数'),
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('价格必须是大于等于0的数字'),
  body('originalPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('原价必须是大于等于0的数字'),
  body('validityDays')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('有效期必须是1-365天之间的整数'),
  body('level')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('级别必须是 beginner、intermediate 或 advanced'),
  body('category')
    .optional()
    .notEmpty()
    .withMessage('分类不能为空')
    .isLength({ max: 50 })
    .withMessage('分类不能超过50个字符'),
  body('features')
    .optional()
    .isArray()
    .withMessage('特色必须是数组格式'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('状态必须是布尔值')
];

const getPackagesValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是大于0的整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  query('category')
    .optional()
    .isLength({ max: 50 })
    .withMessage('分类不能超过50个字符'),
  query('level')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('级别必须是 beginner、intermediate 或 advanced'),
  query('coachId')
    .optional()
    .isUUID()
    .withMessage('教练ID格式不正确'),
  query('minPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最低价格必须是大于等于0的数字'),
  query('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最高价格必须是大于等于0的数字'),
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'price', 'totalSessions'])
    .withMessage('排序字段必须是 createdAt、price 或 totalSessions'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('排序方向必须是 ASC 或 DESC')
];

const packageIdValidation = [
  param('id')
    .isUUID()
    .withMessage('课程包ID格式不正确')
];

// 公开路由
router.get('/', getPackagesValidation, validate, getPackages);
router.get('/:id', packageIdValidation, validate, getPackageById);

// 需要认证的路由
router.use(authenticateToken);

// 教练专用路由
router.post('/', createPackageValidation, validate, createPackage);
router.put('/:id', updatePackageValidation, validate, updatePackage);
router.get('/my/list', getMyPackages);

module.exports = router;
