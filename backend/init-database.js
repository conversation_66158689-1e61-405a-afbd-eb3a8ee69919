#!/usr/bin/env node

/**
 * 数据库初始化脚本
 * 用于创建数据库表结构和初始化测试数据
 */

require('dotenv').config();
const { initDatabase } = require('./src/utils/seedData');

async function main() {
  console.log('🚀 开始初始化 Shuan-Q 数据库...');
  console.log('=====================================');
  
  try {
    await initDatabase();
    console.log('=====================================');
    console.log('✅ 数据库初始化成功完成！');
    console.log('');
    console.log('📋 初始化内容:');
    console.log('  - 数据库表结构同步');
    console.log('  - 课程分类数据');
    console.log('  - 测试用户和教练数据');
    console.log('  - 测试课程包数据');
    console.log('');
    console.log('🎯 下一步: 运行 npm run dev 启动后端服务');
    
    process.exit(0);
  } catch (error) {
    console.error('=====================================');
    console.error('❌ 数据库初始化失败:', error.message);
    console.error('');
    console.error('🔧 请检查:');
    console.error('  - 数据库配置是否正确');
    console.error('  - 依赖包是否已安装');
    console.error('  - 文件权限是否正确');
    
    process.exit(1);
  }
}

// 运行初始化
main();
