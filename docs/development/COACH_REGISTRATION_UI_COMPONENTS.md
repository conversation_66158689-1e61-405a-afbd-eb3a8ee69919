# 🎱 Shuan-Q 教練端注册UI組件梳理

## 📋 概述

本文檔詳細梳理了Shuan-Q台球教練預約平台中，教練端從注册開始的所有頁面需要的功能組件，為前端UI開發提供完整的組件規劃。

## 🔄 教練端用戶流程

```
用戶登錄 → 身份選擇 → 教練資料創建 → 教練資料完善 → 教練端主頁 → 功能模組
```

---

## 📱 頁面組件詳細規劃

### 1. 🔐 用戶登錄頁面 (LoginScreen)

**已實現組件：**
- **頁面佈局組件**
  - `KeyboardAvoidingView`: 鍵盤自適應容器
  - `ScrollView`: 支持滾動的內容區域

**需要的UI組件：**

#### 1.1 頭部組件 (LoginHeader)
```typescript
interface LoginHeaderProps {
  title: string;           // 標題文字
  subtitle?: string;       // 副標題
  logo?: string;          // Logo圖片URL
}
```
**功能：**
- 顯示應用Logo和標題
- 品牌識別和歡迎信息

#### 1.2 手機號輸入組件 (PhoneInput)
```typescript
interface PhoneInputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
}
```
**功能：**
- 手機號格式驗證
- 實時格式化顯示
- 錯誤提示

#### 1.3 驗證碼輸入組件 (CodeInput)
```typescript
interface CodeInputProps {
  value: string;
  onChangeText: (text: string) => void;
  onSendCode: () => void;
  countdown: number;
  loading: boolean;
  disabled?: boolean;
  error?: string;
}
```
**功能：**
- 驗證碼輸入
- 發送驗證碼按鈕
- 倒計時功能
- 加載狀態

#### 1.4 登錄按鈕組件 (LoginButton)
```typescript
interface LoginButtonProps {
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
  title?: string;
}
```

#### 1.5 協議同意組件 (AgreementText)
```typescript
interface AgreementTextProps {
  onUserAgreementPress: () => void;
  onPrivacyPolicyPress: () => void;
}
```

---

### 2. 👤 身份選擇頁面 (RoleSelectionScreen)

**新增頁面 - 需要創建**

#### 2.1 角色卡片組件 (RoleCard)
```typescript
interface RoleCardProps {
  role: 'student' | 'coach';
  title: string;
  description: string;
  icon: string;
  selected: boolean;
  onPress: () => void;
}
```
**功能：**
- 角色選擇卡片
- 選中狀態顯示
- 角色描述信息

#### 2.2 角色選擇容器 (RoleSelector)
```typescript
interface RoleSelectorProps {
  selectedRole: 'student' | 'coach' | null;
  onRoleSelect: (role: 'student' | 'coach') => void;
}
```

#### 2.3 確認按鈕組件 (ConfirmButton)
```typescript
interface ConfirmButtonProps {
  selectedRole: 'student' | 'coach' | null;
  onConfirm: () => void;
  loading?: boolean;
}
```

---

### 3. 📝 教練資料創建頁面 (CoachProfileCreateScreen)

**新增頁面 - 需要創建**

#### 3.1 步驟指示器組件 (StepIndicator)
```typescript
interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  steps: string[];
}
```
**功能：**
- 顯示當前創建步驟
- 進度指示

#### 3.2 個人信息表單組件 (PersonalInfoForm)
```typescript
interface PersonalInfoFormProps {
  data: {
    nickname: string;
    email?: string;
    gender?: 'male' | 'female';
    birthDate?: string;
    location?: string;
  };
  onDataChange: (data: any) => void;
  errors?: Record<string, string>;
}
```

#### 3.3 頭像上傳組件 (AvatarUpload)
```typescript
interface AvatarUploadProps {
  value?: string;
  onUpload: (imageUri: string) => void;
  loading?: boolean;
  error?: string;
}
```

#### 3.4 專長選擇組件 (SpecialtySelector)
```typescript
interface SpecialtySelectorProps {
  selectedSpecialties: string[];
  onSpecialtyToggle: (specialty: string) => void;
  maxSelection?: number;
}
```
**功能：**
- 多選專長標籤
- 最大選擇數量限制

#### 3.5 經驗輸入組件 (ExperienceInput)
```typescript
interface ExperienceInputProps {
  value: number;
  onValueChange: (value: number) => void;
  min?: number;
  max?: number;
}
```

#### 3.6 時薪設置組件 (HourlyRateInput)
```typescript
interface HourlyRateInputProps {
  value: number;
  onValueChange: (value: number) => void;
  currency?: string;
  min?: number;
  max?: number;
}
```

#### 3.7 自我描述組件 (DescriptionInput)
```typescript
interface DescriptionInputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  maxLength?: number;
  minLength?: number;
}
```

---

### 4. ✅ 教練資料完善頁面 (CoachProfileCompleteScreen)

**新增頁面 - 需要創建**

#### 4.1 證書上傳組件 (CertificateUpload)
```typescript
interface CertificateUploadProps {
  certificates: CertificateItem[];
  onAddCertificate: () => void;
  onRemoveCertificate: (id: string) => void;
  loading?: boolean;
}

interface CertificateItem {
  id: string;
  name: string;
  imageUri: string;
  uploadTime: string;
}
```

#### 4.2 教學照片組件 (TeachingPhotos)
```typescript
interface TeachingPhotosProps {
  photos: PhotoItem[];
  onAddPhoto: () => void;
  onRemovePhoto: (id: string) => void;
  maxPhotos?: number;
}
```

#### 4.3 可用時間設置組件 (AvailabilitySchedule)
```typescript
interface AvailabilityScheduleProps {
  schedule: WeeklySchedule;
  onScheduleChange: (schedule: WeeklySchedule) => void;
}

interface WeeklySchedule {
  [key: string]: TimeSlot[]; // monday, tuesday, etc.
}

interface TimeSlot {
  startTime: string;
  endTime: string;
  available: boolean;
}
```

#### 4.4 教學地點組件 (TeachingLocation)
```typescript
interface TeachingLocationProps {
  locations: LocationItem[];
  onAddLocation: () => void;
  onRemoveLocation: (id: string) => void;
  onLocationEdit: (id: string, data: LocationItem) => void;
}

interface LocationItem {
  id: string;
  name: string;
  address: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}
```

---

### 5. 🏠 教練端主頁 (CoachHomeScreen)

**新增頁面 - 需要創建**

#### 5.1 教練儀表板頭部 (CoachDashboardHeader)
```typescript
interface CoachDashboardHeaderProps {
  coach: Coach;
  stats: {
    todayAppointments: number;
    monthlyEarnings: number;
    studentCount: number;
    rating: number;
  };
}
```

#### 5.2 今日預約卡片 (TodayAppointments)
```typescript
interface TodayAppointmentsProps {
  appointments: Appointment[];
  onAppointmentPress: (id: string) => void;
  onViewAll: () => void;
}
```

#### 5.3 快速操作組件 (CoachQuickActions)
```typescript
interface CoachQuickActionsProps {
  actions: QuickActionItem[];
  onActionPress: (actionId: string) => void;
}
```

#### 5.4 收入統計組件 (EarningsChart)
```typescript
interface EarningsChartProps {
  data: EarningsData[];
  period: 'week' | 'month' | 'year';
  onPeriodChange: (period: string) => void;
}
```

#### 5.5 學員動態組件 (StudentActivity)
```typescript
interface StudentActivityProps {
  activities: ActivityItem[];
  onViewMore: () => void;
}
```

---

### 6. 📊 教練資料管理頁面 (CoachProfileScreen)

**需要完善現有頁面**

#### 6.1 資料編輯表單 (ProfileEditForm)
```typescript
interface ProfileEditFormProps {
  profile: CoachProfile;
  onSave: (data: CoachProfile) => void;
  loading?: boolean;
}
```

#### 6.2 認證狀態組件 (VerificationStatus)
```typescript
interface VerificationStatusProps {
  status: 'pending' | 'verified' | 'rejected';
  reason?: string;
  onResubmit?: () => void;
}
```

#### 6.3 評價展示組件 (ReviewsDisplay)
```typescript
interface ReviewsDisplayProps {
  reviews: Review[];
  averageRating: number;
  totalReviews: number;
  onViewAll: () => void;
}
```

---

### 7. 📚 課程包管理頁面 (ManagePackagesScreen)

**需要完善現有頁面**

#### 7.1 課程包列表組件 (PackageList)
```typescript
interface PackageListProps {
  packages: CoursePackage[];
  onPackagePress: (id: string) => void;
  onEditPackage: (id: string) => void;
  onDeletePackage: (id: string) => void;
  loading?: boolean;
}
```

#### 7.2 添加課程包按鈕 (AddPackageButton)
```typescript
interface AddPackageButtonProps {
  onPress: () => void;
  disabled?: boolean;
}
```

#### 7.3 課程包狀態組件 (PackageStatus)
```typescript
interface PackageStatusProps {
  status: 'active' | 'inactive' | 'draft';
  onStatusChange: (status: string) => void;
}
```

---

### 8. ➕ 創建課程包頁面 (CreatePackageScreen)

**需要完善現有頁面**

#### 8.1 課程包信息表單 (PackageInfoForm)
```typescript
interface PackageInfoFormProps {
  data: PackageFormData;
  onDataChange: (data: PackageFormData) => void;
  errors?: Record<string, string>;
}

interface PackageFormData {
  name: string;
  description: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  totalSessions: number;
  price: number;
  originalPrice?: number;
  validityDays: number;
  features: string[];
}
```

#### 8.2 價格設置組件 (PricingInput)
```typescript
interface PricingInputProps {
  price: number;
  originalPrice?: number;
  onPriceChange: (price: number) => void;
  onOriginalPriceChange: (price: number) => void;
  currency?: string;
}
```

#### 8.3 課程特色編輯器 (FeaturesEditor)
```typescript
interface FeaturesEditorProps {
  features: string[];
  onFeaturesChange: (features: string[]) => void;
  maxFeatures?: number;
}
```

#### 8.4 課程預覽組件 (PackagePreview)
```typescript
interface PackagePreviewProps {
  packageData: PackageFormData;
  coach: Coach;
}
```

---

### 9. 📅 預約管理頁面 (ManageAppointmentsScreen)

**需要完善現有頁面**

#### 9.1 預約日曆組件 (AppointmentCalendar)
```typescript
interface AppointmentCalendarProps {
  appointments: Appointment[];
  selectedDate: string;
  onDateSelect: (date: string) => void;
  onAppointmentPress: (appointment: Appointment) => void;
}
```

#### 9.2 預約列表組件 (AppointmentList)
```typescript
interface AppointmentListProps {
  appointments: Appointment[];
  filterStatus?: AppointmentStatus;
  onStatusChange: (id: string, status: AppointmentStatus) => void;
  onAppointmentDetail: (id: string) => void;
}
```

#### 9.3 預約篩選器 (AppointmentFilter)
```typescript
interface AppointmentFilterProps {
  filters: AppointmentFilters;
  onFiltersChange: (filters: AppointmentFilters) => void;
}

interface AppointmentFilters {
  status?: AppointmentStatus[];
  dateRange?: DateRange;
  studentName?: string;
}
```

#### 9.4 預約統計組件 (AppointmentStats)
```typescript
interface AppointmentStatsProps {
  stats: {
    total: number;
    confirmed: number;
    pending: number;
    completed: number;
    cancelled: number;
  };
  period: string;
}
```

---

### 10. 👥 學員管理頁面 (StudentManagementScreen)

**需要完善現有頁面**

#### 10.1 學員列表組件 (StudentList)
```typescript
interface StudentListProps {
  students: Student[];
  onStudentPress: (id: string) => void;
  searchQuery?: string;
  onSearch: (query: string) => void;
}
```

#### 10.2 學員卡片組件 (StudentCard)
```typescript
interface StudentCardProps {
  student: Student;
  stats: StudentStats;
  onPress: () => void;
  onMessagePress: () => void;
}

interface StudentStats {
  totalSessions: number;
  completedSessions: number;
  lastSessionDate?: string;
  nextSessionDate?: string;
}
```

#### 10.3 學員搜索組件 (StudentSearch)
```typescript
interface StudentSearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  filters?: StudentFilters;
  onFiltersChange?: (filters: StudentFilters) => void;
}
```

---

## 🎨 通用UI組件

### 1. 表單組件

#### FormInput - 通用輸入框
```typescript
interface FormInputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  keyboardType?: KeyboardTypeOptions;
  multiline?: boolean;
  numberOfLines?: number;
  maxLength?: number;
  error?: string;
  required?: boolean;
  disabled?: boolean;
}
```

#### FormPicker - 選擇器
```typescript
interface FormPickerProps {
  label: string;
  selectedValue: string;
  onValueChange: (value: string) => void;
  items: PickerItem[];
  placeholder?: string;
  error?: string;
  required?: boolean;
}
```

#### FormSwitch - 開關
```typescript
interface FormSwitchProps {
  label: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
  description?: string;
}
```

### 2. 佈局組件

#### Section - 頁面區塊
```typescript
interface SectionProps {
  title?: string;
  children: React.ReactNode;
  style?: ViewStyle;
  headerAction?: React.ReactNode;
}
```

#### Card - 卡片容器
```typescript
interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  elevation?: number;
}
```

### 3. 反饋組件

#### LoadingSpinner - 加載指示器
```typescript
interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  text?: string;
}
```

#### EmptyState - 空狀態
```typescript
interface EmptyStateProps {
  title: string;
  description?: string;
  icon?: string;
  actionText?: string;
  onAction?: () => void;
}
```

#### Toast - 提示信息
```typescript
interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  onDismiss?: () => void;
}
```

---

## 🔄 狀態管理

### Redux Slices 需求

#### coachSlice
```typescript
interface CoachState {
  profile: CoachProfile | null;
  packages: CoursePackage[];
  appointments: Appointment[];
  students: Student[];
  earnings: EarningsData[];
  loading: {
    profile: boolean;
    packages: boolean;
    appointments: boolean;
    students: boolean;
  };
  errors: {
    profile: string | null;
    packages: string | null;
    appointments: string | null;
    students: string | null;
  };
}
```

#### Actions 需求
- `createCoachProfile`
- `updateCoachProfile`
- `uploadCertificate`
- `setAvailability`
- `fetchCoachPackages`
- `createPackage`
- `updatePackage`
- `deletePackage`
- `fetchAppointments`
- `updateAppointmentStatus`
- `fetchStudents`
- `fetchEarnings`

---

## 📋 開發優先級

### 高優先級 (P0)
1. **身份選擇頁面** - 用戶流程必需
2. **教練資料創建頁面** - 核心注册流程
3. **教練端主頁** - 主要工作界面

### 中優先級 (P1)
1. **教練資料管理頁面** - 資料維護
2. **課程包管理頁面** - 業務核心
3. **創建課程包頁面** - 業務核心

### 低優先級 (P2)
1. **預約管理頁面** - 業務管理
2. **學員管理頁面** - 高級功能
3. **教練資料完善頁面** - 可選增強

---

## 🎯 UI/UX 設計原則

### 視覺設計
- **主色調**: #5a9178 (台球綠)
- **輔助色**: #1890ff (信息藍)、#52c41a (成功綠)、#fa8c16 (警告橙)
- **圓角**: 12px 統一圓角
- **間距**: 8px 基礎單位

### 交互設計
- **反饋及時**: 所有操作提供即時反饋
- **狀態明確**: 清晰的加載、成功、錯誤狀態
- **操作簡化**: 減少用戶操作步驟
- **信息層次**: 重要信息突出顯示

### 無障礙設計
- **對比度**: 符合WCAG標準
- **字體大小**: 支持系統字體縮放
- **觸摸目標**: 不小於44px
- **語義化**: 正確使用accessibility屬性

---

## 📚 技術實現建議

### 組件架構
- 使用TypeScript確保類型安全
- 採用函數式組件和Hooks
- 實現組件懶加載優化性能
- 統一的錯誤邊界處理

### 狀態管理
- Redux Toolkit管理全局狀態
- React Query處理服務器狀態
- 本地狀態使用useState/useReducer
- 表單狀態使用react-hook-form

### 性能優化
- 使用React.memo防止不必要重渲染
- 實現虛擬列表處理大數據
- 圖片懶加載和壓縮
- 代碼分割和預加載

---

## 📖 結語

本文檔為Shuan-Q教練端UI開發提供了完整的組件規劃。建議按照優先級順序開發，先實現核心流程，再完善高級功能。所有組件都應該遵循統一的設計規範和編碼標準，確保用戶體驗的一致性和代碼的可維護性。 