# SHUAN-Q 台球教練預約平台 - 前端頁面結構

## 🏗️ 底部導航結構

```
應用主架構
├── 首頁 (Home)
├── 商城 (Mall)
├── 教練 (Coach)
└── 我的 (Profile)
```

---

## 📱 首頁模組

### 1. 首頁 (HomeScreen)
- Banner輪播圖（自動輪播，點擊跳轉詳情）
- 搜索欄（點擊跳轉搜索頁面）
- 本周推薦商品（簡易商品卡片，包含商品縮略圖、商品名稱、商品價格，點擊可進入商品詳情頁面）
- 新進教練（教練縮略卡片，包含圓形頭像、教練姓名、教練級別標籤、所在城市標籤、常駐球館名稱，點擊進入詳情按鈕），點擊整個縮略卡片可進入教練主頁

### 2. 搜索頁面 (SearchScreen)
- 搜索輸入框
- 搜索歷史記錄列表
- 熱門搜索標籤
- 搜索結果列表（商品卡片+教練卡片混合顯示）

### 3. 商品詳情頁 (ProductDetailScreen)
- 商品圖片輪播
- 商品標題
- 價格信息（現價、原價）
- 商品規格選擇器
- 商品描述內容
- 立即購買按鈕
- 加入購物車按鈕
- 評價列表（用戶頭像、評分、評價內容）

### 4. 分類商品頁 (CategoryProductsScreen)
- 頁面標題欄
- 分類篩選標籤
- 排序選項（價格、銷量、評分）
- 商品網格列表（商品卡片）

---

## 🛒 商城模組

### 1. 商城首頁 (MallScreen)
- 頂部導航欄（返回按鈕、標題、菜單按鈕）
- 搜索欄（點擊跳轉商品搜索）
- 分類標籤欄（品牌、品類、銷量、價格）
- 商品網格列表（商品卡片，包含熱銷標籤、商品圖片、商品名稱、價格、銷量）

### 2. 商品搜索頁 (ProductSearchScreen)
- 搜索輸入框
- 篩選按鈕（彈出篩選面板）
- 排序選項
- 搜索結果網格（商品卡片）

### 3. 商品詳情頁 (ProductDetailScreen)
- 與首頁模組共享

### 4. 分類商品頁 (CategoryProductsScreen)
- 與首頁模組共享

---

## 👨‍🏫 教練模組

### 1. 教練列表頁 (CoachListScreen)
- 頂部導航欄（返回、標題、搜索）
- 篩選標籤欄（全部、初級教練、中級教練、高級教練、金牌教練）
- 篩選按鈕（彈出高級篩選面板）
- 教練卡片列表（頭像、在線狀態點、教練姓名、級別標籤、經驗描述、評分星級、學員數、專長標籤、價格、預約按鈕）

### 2. 教練詳情頁 (CoachDetailScreen)
- 教練頭部卡片（頭像、姓名、級別、在線狀態、評分、學員數、經驗年限）
- 教練詳細信息（專長領域、自我介紹、認證證書）
- 課程包列表（課程包卡片，包含課程圖片、課程名稱、課程描述、價格、購買按鈕）
- 評價列表（學員頭像、姓名、評分、評價內容、評價時間）
- 底部預約按鈕

### 3. 教練搜索頁 (CoachSearchScreen)
- 搜索輸入框
- 篩選條件（地區、級別、專長、價格範圍）
- 教練列表（簡化版教練卡片）

### 4. 課程包詳情頁 (PackageDetailScreen)
- 課程包圖片輪播
- 課程包標題
- 教練信息卡片（頭像、姓名、級別）
- 價格信息（現價、原價、優惠標籤）
- 課程內容描述
- 課程大綱列表
- 立即購買按鈕
- 評價列表

### 5. 創建預約頁 (AppointmentCreateScreen)
- 教練信息展示
- 日期選擇器（日曆組件）
- 時間段選擇器（可選時間列表）
- 預約信息表單（聯繫電話、備註信息）
- 價格確認
- 提交預約按鈕

---

## 👤 個人中心模組

### 1. 個人資料頁 (ProfileScreen)
- 用戶頭部卡片（頭像、姓名、會員等級、編輯按鈕）
- 賬戶統計卡片（餘額、積分、會員天數）
- 功能統計網格（我的預約、學習課程、我的收藏、我的評價）
- 訂單功能區（待付款、待確認、進行中、已完成）
- 學習功能區（學習記錄、學習計劃、練習報告、技能證書）
- 設置選項列表（個人資料、安全設置、隱私設置、消息設置、意見反饋、關於我們、客服中心）

### 2. 編輯資料頁 (EditProfileScreen)
- 頁面標題欄（返回、標題、保存）
- 頭像上傳區域（當前頭像、相機圖標）
- 個人信息表單（姓名、電話、郵箱、性別、生日、地址）
- 保存按鈕

### 3. 我的訂單頁 (MyOrdersScreen)
- 頁面標題欄
- 訂單狀態標籤欄（全部、待付款、待確認、進行中、已完成）
- 訂單卡片列表（訂單號、商品圖片、商品名稱、價格、數量、狀態、操作按鈕）

### 4. 我的預約頁 (MyAppointmentsScreen)
- 頁面標題欄
- 預約卡片列表（教練頭像、教練姓名、預約時間、課程類型、狀態標籤、操作按鈕）

### 5. 設置頁面 (SettingsScreen)
- 頁面標題欄
- 設置項列表（通知設置、隱私設置、語言設置、字體大小、清除緩存、版本信息）
- 退出登錄按鈕

### 教練專用頁面

### 6. 教練資料頁 (CoachProfileScreen)
- 頁面標題欄
- 教練頭部卡片（頭像、認證標識、姓名、級別、專業領域）
- 認證信息區域（證書列表、認證狀態）
- 教學成果展示（學員數量、評分、教學年限、獲獎記錄）
- 業務統計（本月收入、預約數量、好評率）

### 7. 課程包管理頁 (ManagePackagesScreen)
- 頁面標題欄（返回、標題、新增按鈕）
- 課程包卡片列表（課程圖片、課程名稱、價格、狀態標籤、編輯按鈕、刪除按鈕）

### 8. 預約管理頁 (ManageAppointmentsScreen)
- 頁面標題欄
- 預約狀態篩選標籤
- 預約卡片列表（學員頭像、學員姓名、預約時間、課程類型、狀態、操作按鈕）

### 9. 學員管理頁 (StudentManagementScreen)
- 頁面標題欄
- 搜索欄
- 學員卡片列表（學員頭像、姓名、級別、學習進度、最後上課時間、聯繫按鈕）

### 10. 創建課程包頁 (CreatePackageScreen)
- 頁面標題欄（返回、標題、保存）
- 課程包圖片上傳
- 課程包基本信息表單（名稱、描述、課程數量、有效期）
- 價格設置（原價、現價、優惠標籤）
- 課程大綱編輯器
- 提交按鈕

### 11. 編輯課程包頁 (EditPackageScreen)
- 頁面標題欄（返回、標題、保存）
- 課程包圖片編輯
- 課程包信息編輯表單
- 狀態控制開關（上架/下架）
- 保存修改按鈕

---

## 🔐 認證模組

### 1. 登錄頁面 (LoginScreen)
- 應用Logo
- 登錄標題
- 手機號輸入框
- 獲取驗證碼按鈕
- 驗證碼輸入框
- 登錄按鈕
- 服務協議和隱私政策勾選框

---

## 📝 頁面內容說明

| 頁面類型 | 主要內容 |
|----------|----------|
| **列表頁** | 標題、搜索欄、篩選選項、卡片列表 |
| **詳情頁** | 圖片展示、詳細信息、操作按鈕、評價區域 |
| **表單頁** | 輸入字段、選擇器、上傳組件、提交按鈕 |
| **管理頁** | 數據列表、狀態標籤、操作按鈕、篩選選項 |

---

**備註**: 此文檔專注於頁面結構和內容梳理，便於修改和調整。 